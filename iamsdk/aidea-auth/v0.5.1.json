[{"id": "menu.homepage", "label": "首页", "children": [{"id": "action.homepage.view", "label": "查看首页"}]}, {"id": "menu.dataCollect", "label": "运营看板", "children": [{"id": "menu.dataCollect.manager.stat", "label": "运营看板", "children": [{"id": "action.dataCollect.manager.stat.view", "label": "查看运营看板"}]}, {"id": "menu.dataCollect.taskCenter", "label": "任务中心", "children": [{"id": "action.dataCollect.taskCenter.task.view", "label": "查看任务"}, {"id": "action.dataCollect.taskCenter.task.add", "label": "新建任务", "api": "POST:/api/v1/collect/<EMAIL>"}, {"id": "action.dataCollect.taskCenter.task.edit", "label": "编辑任务", "api": "PUT:/api/v1/collect/task/*@task.edit"}, {"id": "action.dataCollect.taskCenter.task.errorTag.view", "label": "查看任务错因标签"}, {"id": "action.dataCollect.taskCenter.task.errorTag.edit", "label": "编辑任务错因标签", "api": "PUT:/api/v1/collect/task/*@task.errorTag.edit"}, {"id": "action.dataCollect.taskCenter.task.delete", "label": "删除任务", "api": "DELETE:/api/v1/collect/task/*@task.delete"}, {"id": "action.dataCollect.taskCenter.task.status", "label": "发布/归档/恢复 任务", "api": "PUT:/api/v1/collect/task/*@task.status"}, {"id": "action.dataCollect.taskCenter.task.export", "label": "导出任务数据"}, {"id": "action.dataCollect.taskCenter.task.progress.collectCount.view", "label": "查看采集量"}, {"id": "action.dataCollect.taskCenter.task.progress.collectCount.filter", "label": "刷选采集量"}, {"id": "action.dataCollect.taskCenter.task.progress.checkCount.view", "label": "查看审核量"}, {"id": "action.dataCollect.taskCenter.task.progress.checkCount.filter", "label": "刷选审核量"}, {"id": "action.dataCollect.taskCenter.task.user.collector.add", "label": "添加采集员", "api": "POST:/api/v1/collect/task/<EMAIL>"}, {"id": "action.dataCollect.taskCenter.task.user.checker.add", "label": "添加审核员", "api": "POST:/api/v1/collect/task/<EMAIL>"}, {"id": "action.dataCollect.taskCenter.task.user.collector.status", "label": "更改采集员状态", "api": "PUT:/api/v1/collect/task/user/*@collector.status"}, {"id": "action.dataCollect.taskCenter.task.user.checker.status", "label": "更改审核员状态", "api": "PUT:/api/v1/collect/task/user/*@checker.status"}, {"id": "action.dataCollect.taskCenter.task.job.add", "label": "生成实例任务", "api": "POST:/api/v1/collect/task/<EMAIL>"}, {"id": "action.dataCollect.taskCenter.task.job.edit", "label": "编辑实例任务", "api": "PUT:/api/v1/collect/task/job/*@job.edit"}, {"id": "action.dataCollect.taskCenter.task.job.delete", "label": "删除实例任务", "api": "DELETE:/api/v1/collect/task/<EMAIL>"}, {"id": "action.dataCollect.taskCenter.task.job.export", "label": "导出实例任务数据"}, {"id": "action.dataCollect.taskCenter.task.job.deliver", "label": "快速交付", "api": "PUT:/api/v1/collect/review/<EMAIL>"}, {"id": "action.dataCollect.taskCenter.task.job.check", "label": "审核实例任务"}, {"id": "action.dataCollect.taskCenter.task.job.claim", "label": "采集实例任务", "api": "POST:/api/v1/collect/task/job/<EMAIL>"}, {"id": "action.dataCollect.taskCenter.task.job.episode.view", "label": "查看episode"}, {"id": "action.dataCollect.taskCenter.task.job.episode.delete", "label": "删除episode", "api": "DELETE:/api/v1/collect/task/job/assignment/episode/*@episode.delete"}, {"id": "action.dataCollect.taskCenter.task.job.episode.check", "api": "POST:/api/v1/collect/<EMAIL>", "label": "标注episode"}, {"id": "action.dataCollect.taskCenter.task.job.episode.invalid", "label": "episode转无效", "api": "PUT:/api/v1/collect/task/job/assignment/episode/*@episode.invalid"}]}, {"id": "menu.dataCollect.device", "label": "设备中心", "children": [{"id": "action.dataCollect.device.view", "label": "查看设备"}, {"id": "action.dataCollect.device.edit", "label": "编辑设备", "api": "PUT:/api/v1/collect/<EMAIL>"}, {"id": "action.dataCollect.device.add", "label": "新增设备", "api": "POST:/api/v1/collect/<EMAIL>"}, {"id": "action.dataCollect.device.delete", "label": "删除设备", "api": "DELETE:/api/v1/collect/<EMAIL>"}]}, {"id": "menu.dataCollect.sceneResource.resource", "label": "物体库", "children": [{"id": "action.dataCollect.sceneResource.resource.view", "label": "查看物体"}, {"id": "action.dataCollect.sceneResource.resource.add", "label": "新建物体", "api": "POST:/api/v1/collect/task/<EMAIL>"}, {"id": "action.dataCollect.sceneResource.resource.edit", "label": "编辑物体", "api": "PUT:/api/v1/collect/task/resource/*@resource.edit"}, {"id": "action.dataCollect.sceneResource.resource.delete", "label": "删除物体", "api": "DELETE:/api/v1/collect/task/resource/*@resource.delete"}]}]}, {"id": "menu.tagManage", "label": "标签管理", "children": [{"id": "menu.tagManage.task", "label": "任务标签", "children": [{"id": "action.tagManage.task.view", "label": "查看任务标签"}, {"id": "action.tagManage.task.add", "label": "新增任务标签", "api": "POST:/api/v1/<EMAIL>"}, {"id": "action.tagManage.task.delete", "label": "删除任务标签", "api": "DELETE:/api/v1/<EMAIL>"}, {"id": "action.tagManage.task.edit", "label": "编辑任务标签", "api": "PUT:/api/v1/<EMAIL>"}]}, {"id": "menu.tagManage.taskResource", "label": "物体标签", "children": [{"id": "action.tagManage.taskResource.view", "label": "查看物体标签"}, {"id": "action.tagManage.taskResource.add", "label": "新增物体标签", "api": "POST:/api/v1/<EMAIL>"}, {"id": "action.tagManage.taskResource.delete", "label": "删除物体标签", "api": "DELETE:/api/v1/<EMAIL>"}, {"id": "action.tagManage.taskResource.edit", "label": "编辑物体标签", "api": "PUT:/api/v1/<EMAIL>"}]}]}, {"id": "menu.helpCenter", "label": "帮助中心", "children": [{"id": "menu.helpCenter.releaseNote", "label": "Release Notes", "children": [{"id": "action.helpCenter.releaseNote.view", "label": "查看Release Notes"}]}, {"id": "menu.helpCenter.userNote", "label": "用户手册", "children": [{"id": "action.helpCenter.userNote.view", "label": "查看用户手册"}]}]}, {"id": "menu.userCenter", "label": "用户中心", "children": [{"id": "menu.userCenter.user", "label": "用户管理", "children": [{"id": "action.userCenter.user.view", "label": "查看用户"}, {"id": "action.userCenter.user.add", "api": "POST:/api/v1/<EMAIL>", "label": "新增用户"}, {"id": "action.userCenter.user.edit", "api": "PUT:/api/v1/<EMAIL>", "label": "编辑用户"}, {"id": "action.userCenter.user.delete", "api": "POST:/api/v1/user/<EMAIL>", "label": "删除用户"}]}, {"id": "menu.userCenter.role", "label": "角色管理", "children": [{"id": "action.userCenter.role.view", "label": "查看角色"}, {"id": "action.userCenter.role.add", "api": "POST:/api/v1/user/<EMAIL>", "label": "新增角色"}, {"id": "action.userCenter.role.edit", "api": "PUT:/api/v1/user/<EMAIL>", "label": "编辑角色"}, {"id": "action.userCenter.role.delete", "api": "DELETE:/api/v1/user/role/*@role.delete", "label": "删除角色"}]}, {"id": "menu.userCenter.userGroup", "label": "用户组管理", "children": [{"id": "action.userCenter.userGroup.view", "label": "查看用户组"}, {"id": "action.userCenter.userGroup.add", "api": "POST:/api/v1/user/<EMAIL>", "label": "新增用户组"}, {"id": "action.userCenter.userGroup.edit", "api": "PUT:/api/v1/user/<EMAIL>", "label": "编辑用户组"}, {"id": "action.userCenter.userGroup.delete", "api": "DELETE:/api/v1/user/<EMAIL>", "label": "删除用户组"}]}]}]