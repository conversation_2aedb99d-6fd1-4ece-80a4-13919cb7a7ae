package dataio

import (
	"context"
	"encoding/json"
	"fmt"
	"maps"

	"aim-data/internal/codes"
	"aim-data/internal/consts"
	"aim-data/internal/dao"
	"aim-data/internal/logic/collect"
	"aim-data/internal/model"
	"aim-data/internal/model/entity"
	"aim-data/internal/service"
	"aim-data/utility"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"github.com/jinzhu/copier"
)

type Action struct {
	Duration int    `json:"duration"`
	Offset   int    `json:"offset"`
	Atomic   int    `json:"atomic"`
	Desc     string `json:"desc"`
}

func (s *sDataIO) ImportTask(ctx context.Context, input *model.ImportTaskInput) (output *model.ImportTaskOutput, err error) {
	tenant, err := service.Tenant().GetTenant(ctx, fmt.Sprintf("%d", input.TenantId))
	if err != nil {
		return nil, err
	}
	ctx = context.WithValue(ctx, consts.CtxAccessTenantConfigKey, tenant.Config)
	ctx = context.WithValue(ctx, consts.CtxAccessTenantIdKey, uint(tenant.Id))
	if collect.ExistTask(ctx, input.Asset.Task.Name, 0) {
		input.Asset.Task.Name = input.Asset.Task.Name + "-" + utility.RandStr(10)
	}
	var taskID int64
	allPathMappings := make([]model.TaskPathMapping, 0)
	allDirPathMappings := make([]model.TaskPathMapping, 0)
	idMap := make(map[uint]uint)
	resTagIDMap := make(map[uint]uint)

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		newTask := entity.Task{
			TenantId:    input.TenantId,
			Name:        input.Asset.Task.Name,
			Type:        input.Asset.Task.Type,
			Status:      input.Asset.Task.Status,
			Config:      input.Asset.Task.Config,
			Creator:     "system",
			CreatorName: "system",
			CreatorId:   "system",
			Updater:     "system",
			UpdaterName: "system",
			UpdaterId:   "system",
		}
		taskID, err = dao.Task.Ctx(ctx).InsertAndGetId(newTask)
		if err != nil {
			return err
		}

		tags, taskTagIDMappings, err := getNewTags(ctx, input.Asset.Task.Tags.TaskTags, input.Asset.Task.Tags.ParentTagDetail)
		if err != nil {
			return err
		}
		maps.Copy(resTagIDMap, taskTagIDMappings)

		tagsId := make([]uint, 0, len(tags))
		for _, tag := range tags {
			for _, t := range tag {
				tagsId = append(tagsId, t.Id)
			}
		}
		_, err = service.Tag().Bound(tx, uint(taskID), dao.Task.Table(), tagsId)
		if err != nil {
			return err
		}

		jobTemp := new(entity.JobTemplate)
		if err = copier.Copy(jobTemp, &input.Asset.Task.JobTemplate); err != nil {
			return err
		}
		jobTemp.TaskId = uint(taskID)
		jobTemp.TenantId = input.TenantId
		actions := make([]Action, 0)
		err = json.Unmarshal([]byte(jobTemp.ActionStep), &actions)
		if err != nil {
			return err
		}
		for i := range actions {
			if v, ok := taskTagIDMappings[uint(actions[i].Atomic)]; ok {
				actions[i].Atomic = int(v)
			}
		}
		marshal, err := json.Marshal(actions)
		if err != nil {
			return err
		}
		jobTemp.ActionStep = string(marshal)

		_, err = dao.JobTemplate.Ctx(ctx).Insert(jobTemp)
		if err != nil {
			return err
		}

		deviceTags, deviceTagIDMappings, err := getNewTags(ctx, input.Asset.Task.Tags.DeviceTags, input.Asset.Task.Tags.ParentTagDetail)
		if err != nil {
			return err
		}
		maps.Copy(resTagIDMap, deviceTagIDMappings)

		if input.Asset.Task.DeviceType != nil {
			deviceTypePathMappings, deviceTypeDirPathMappings, err := syncDeviceType(ctx, uint(taskID),
				*input.Asset.Task.DeviceType, deviceTags, input.Asset.Task.Tags.ParentTagDetail)
			if err != nil {
				return err
			}
			allPathMappings = append(allPathMappings, deviceTypePathMappings...)
			allDirPathMappings = append(allDirPathMappings, deviceTypeDirPathMappings...)
		}

		contentPathMappings, err := syncTaskContent(ctx, uint(taskID), input.Asset.Task.Content)
		if err != nil {
			return err
		}
		allPathMappings = append(allPathMappings, contentPathMappings...)

		_, taskResourceTagMappings, err := getNewTags(ctx, input.Asset.Task.Tags.TaskResourceTags, input.Asset.Task.Tags.ParentTagDetail)
		if err != nil {
			return err
		}
		maps.Copy(resTagIDMap, taskResourceTagMappings)

		resourcePathMappings, m, err := syncTaskResources(ctx, tx, input.Asset.Task.TaskResources, taskResourceTagMappings)
		if err != nil {
			return err
		}
		idMap = m
		allPathMappings = append(allPathMappings, resourcePathMappings...)

		var vars []*model.TaskVariableDetail
		for _, v := range input.Asset.Task.TaskVariables {
			if v.JobId == 0 {
				vars = append(vars, &model.TaskVariableDetail{
					TenantId: input.TenantId,
					TaskID:   uint(taskID),
					AddTaskVariableInput: &model.AddTaskVariableInput{
						Type:        uint(v.Type),
						Name:        v.Name,
						DisplayName: v.DisplayName,
						Values:      v.Values,
					},
				})
			}
		}
		if len(vars) > 0 {
			return collect.AddTaskVariable(tx, vars)
		}
		return nil
	})
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeCreateTaskJobFailed, err)
	}
	return &model.ImportTaskOutput{
		TaskID:            uint(taskID),
		PathMappings:      allPathMappings,
		DirPathMappings:   allDirPathMappings,
		TaskResourceIDMap: idMap,
		TagsIDMap:         resTagIDMap,
	}, nil
}

func (s *sDataIO) ImportTaskJob(ctx context.Context, input *model.ImportTaskJobInput) (output *model.ImportTaskJobOutput, err error) {
	tenant, err := service.Tenant().GetTenant(ctx, fmt.Sprintf("%d", input.TenantId))
	if err != nil {
		return nil, err
	}
	var jobID int64
	ctx = context.WithValue(ctx, consts.CtxAccessTenantConfigKey, tenant.Config)
	ctx = context.WithValue(ctx, consts.CtxAccessTenantIdKey, uint(tenant.Id))
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// Step1 插入新Job
		newJob := new(entity.Job)
		if err = copier.Copy(newJob, &input.Job); err != nil {
			return err
		}
		// 更新job的extra字段，修改对应原子能力的TagID
		actions := make([]Action, 0)
		err = json.Unmarshal([]byte(newJob.Extra), &actions)
		if err != nil {
			return err
		}
		for i := range actions {
			if v, ok := input.TagsIDMap[uint(actions[i].Atomic)]; ok {
				actions[i].Atomic = int(v)
			}
		}
		marshal, err := json.Marshal(actions)
		if err != nil {
			return err
		}
		newJob.Extra = string(marshal)
		newJob.Id = 0
		newJob.TaskId = input.TaskID
		newJob.TenantId = input.TenantId
		jobID, err = tx.Model(dao.Job.Table()).InsertAndGetId(newJob)
		if err != nil {
			return err
		}

		taskVariable := make([]entity.TaskVariable, 0)
		jobRelResourceId := make([]uint, 0)
		for _, item := range input.TaskVariables {
			if item.JobId == input.Job.Id {
				newVar := entity.TaskVariable{
					TenantId:    input.TenantId,
					TaskId:      input.TaskID,
					JobId:       uint(jobID),
					Type:        item.Type,
					Name:        item.Name,
					DisplayName: item.DisplayName,
					Values:      item.Values,
					Status:      item.Status,
				}
				if item.Type == 1 {
					newVar.Values = genNewValues(item.Values, input.TaskResourceIDMap)
					jobRelResourceId = append(jobRelResourceId, getIdFromValues(newVar.Values)...)
				}
				taskVariable = append(taskVariable, newVar)
			}
		}
		if len(taskVariable) > 0 {
			_, err = tx.Model(dao.TaskVariable.Table()).Insert(taskVariable)
			if err != nil {
				return err
			}
		}
		if len(jobRelResourceId) > 0 {
			jobRelResourceId = utility.DistinctUnit(jobRelResourceId)
			jobRels := make([]entity.JobResourceRel, 0)
			for _, resID := range jobRelResourceId {
				jobRels = append(jobRels, entity.JobResourceRel{
					TaskId:         input.TaskID,
					JobId:          uint(jobID),
					TaskResourceId: resID,
				})
			}
			_, err = tx.Model(dao.JobResourceRel.Table()).Insert(jobRels)
			if err != nil {
				return err
			}
		}
		return nil
	})

	return &model.ImportTaskJobOutput{
		JobID:        uint(jobID),
		PathMappings: make([]model.TaskPathMapping, 0),
	}, nil
}
