package dataio

import (
	"aim-data/internal/service"
	"aim-data/utility"
	"context"
	"encoding/json"

	"github.com/gogf/gf/v2/frame/g"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/util/gconv"

	"aim-data/internal/dao"
	"aim-data/internal/model"
	"aim-data/internal/model/do"
	"aim-data/internal/model/entity"
)

type ContentItem struct {
	Insert     interface{}            `json:"insert"`
	Attributes map[string]interface{} `json:"attributes,omitempty"`
}

// ContentWrapper 表示完整的content结构
type ContentWrapper struct {
	Ops []ContentItem `json:"ops"`
}

func getNewTags(ctx context.Context, tags map[string][]entity.Tag, parentTagDetail map[string]*model.SyncTag) (
	newTags map[string][]entity.Tag, tagIdMapping map[uint]uint, err error) {

	tenantId := utility.GetAccessTenantId(ctx)
	newTags = make(map[string][]entity.Tag)
	tagIDMappings := make(map[uint]uint)
	// 遍历每个父标签及其子标签
	for parentCode, childTags := range tags {
		// 查询父标签是否存在
		parentTag, err := ensureTagExists(ctx, parentTagDetail[parentCode], parentTagDetail, int(tenantId))
		if err != nil {
			return nil, nil, err
		}

		// 处理子标签
		for _, childTag := range childTags {
			// 查询子标签是否存在
			var existingChildTag *entity.Tag
			err := dao.Tag.Ctx(ctx).
				Where(dao.Tag.Columns().TenantId, tenantId).
				Where(dao.Tag.Columns().Code, childTag.Code).
				Scan(&existingChildTag)
			if err != nil {
				g.Log().Errorf(ctx, "failed to check tag[%s] exist, %v", childTag.Code, err)
				continue
			}
			if existingChildTag == nil {
				// 子标签不存在，创建新的
				newTagID, err := dao.Tag.Ctx(ctx).InsertAndGetId(&entity.Tag{
					TenantId: tenantId,
					Code:     childTag.Code,
					Label:    childTag.Label,
					Pid:      int(parentTag.Id),
					Level:    childTag.Level,
					Status:   childTag.Status,
				})
				if err != nil {
					continue
				}
				newTags[parentCode] = append(newTags[parentCode], entity.Tag{
					Id:       uint(newTagID),
					TenantId: tenantId,
					Code:     childTag.Code,
					Label:    childTag.Label,
					Pid:      int(parentTag.Id),
					Level:    childTag.Level,
					Status:   childTag.Status,
				})
				tagIDMappings[childTag.Id] = uint(newTagID)
			} else {
				newTags[parentCode] = append(newTags[parentCode], entity.Tag{
					Id:       existingChildTag.Id,
					TenantId: tenantId,
					Code:     childTag.Code,
					Label:    childTag.Label,
					Pid:      int(parentTag.Id),
					Level:    childTag.Level,
					Status:   childTag.Status,
				})
				tagIDMappings[childTag.Id] = existingChildTag.Id
			}
		}
	}
	return newTags, tagIDMappings, nil
}

func syncDeviceType(ctx context.Context, taskId uint, syncDeviceType model.SyncDeviceType, tags map[string][]entity.Tag, parentTagDetail map[string]*model.SyncTag) (
	pathMapping, dirPathMapping []model.TaskPathMapping, err error) {

	var deviceType *model.SyncDeviceType
	err = dao.DeviceType.Ctx(ctx).Where(dao.DeviceType.Columns().Name, syncDeviceType.Name).Scan(&deviceType)
	if err != nil {
		return nil, nil, err
	}
	var deviceID uint
	pathMapping = make([]model.TaskPathMapping, 0)
	dirPathMapping = make([]model.TaskPathMapping, 0)
	if deviceType == nil {
		deviceID, pathMapping, dirPathMapping, err = createNewDeviceType(ctx, syncDeviceType, tags)
		if err != nil {
			return nil, nil, err
		}
	} else {
		deviceID = deviceType.Id
	}
	_, err = dao.Task.Ctx(ctx).Where(dao.Task.Columns().Id, taskId).
		Update(do.Task{DeviceTypeId: deviceID})
	if err != nil {
		return nil, nil, err
	}
	return pathMapping, dirPathMapping, nil
}

func syncTaskContent(ctx context.Context, taskID uint, content string) ([]model.TaskPathMapping, error) {
	// 解析content为JSON
	res := make([]model.TaskPathMapping, 0)
	var wrapper ContentWrapper
	if err := json.Unmarshal([]byte(content), &wrapper); err != nil {
		return res, nil
	}

	// 遍历所有操作项
	for i := range wrapper.Ops {
		// 检查是否是图片或视频
		if insertMap, ok := wrapper.Ops[i].Insert.(map[string]interface{}); ok {
			if imageURL, ok := insertMap["image"].(string); ok {
				newPath, err := utility.GenTaskPathMappings(ctx, imageURL)
				if err != nil {
					return nil, err
				}
				insertMap["image"] = newPath.Target
				wrapper.Ops[i].Insert = insertMap
				res = append(res, newPath)
			}
			// 处理视频
			if videoURL, ok := insertMap["video"].(string); ok {
				// 对视频URL进行预签名
				newPath, err := utility.GenTaskPathMappings(ctx, videoURL)
				if err != nil {
					return nil, err
				}
				insertMap["video"] = newPath.Target
				wrapper.Ops[i].Insert = insertMap
			}
		}
	}

	processedContent, err := json.Marshal(wrapper)
	_, err = dao.Task.Ctx(ctx).Where(dao.Task.Columns().Id, taskID).
		Update(do.Task{Content: string(processedContent)})
	if err != nil {
		return nil, err
	}
	return res, nil
}

func syncTaskResources(ctx context.Context, tx gdb.TX, originResources []model.SyncTaskResource, tagIdMappings map[uint]uint) ([]model.TaskPathMapping, map[uint]uint, error) {
	res := make([]model.TaskPathMapping, 0)
	idMap := make(map[uint]uint)
	for _, item := range originResources {
		newPath, err := utility.GenTaskPathMappings(ctx, item.Img)
		if err != nil {
			return nil, nil, err
		}
		res = append(res, newPath)
		taskResource := entity.TaskResource{
			TenantId:    utility.GetAccessTenantId(ctx),
			Name:        item.Name,
			Img:         newPath.Target,
			Content:     item.Content,
			Extra:       item.Extra,
			Creator:     "system",
			CreatorName: "system",
			CreatorId:   "system",
			Updater:     "system",
		}
		id, err := dao.TaskResource.Ctx(ctx).InsertAndGetId(&taskResource)
		if err != nil {
			return nil, nil, err
		}
		idMap[item.Id] = uint(id)
		newTagsID := make([]uint, 0, len(item.TagIDs))
		if len(item.TagIDs) > 0 {
			for _, tagID := range item.TagIDs {
				if newTagID, ok := tagIdMappings[uint(tagID)]; ok {
					newTagsID = append(newTagsID, newTagID)
				}
			}
			if len(newTagsID) > 0 {
				_, err = service.Tag().Bound(tx, uint(id), dao.TaskResource.Table(), newTagsID)
				if err != nil {
					return nil, nil, err
				}
			}
		}
	}
	return res, idMap, nil
}

func createNewDeviceType(ctx context.Context, syncDeviceType model.SyncDeviceType, newTags map[string][]entity.Tag) (deviceTypeID uint, pathMapping, dirPathMapping []model.TaskPathMapping, err error) {

	tenantId := utility.GetAccessTenantId(ctx)
	var (
		taskCollectOntologyId uint
		taskEndKindId         uint
		taskEndRightID        uint
	)

	if _, ok := newTags["task_collect_ontology"]; ok && len(newTags["task_collect_ontology"]) > 0 {
		taskCollectOntologyId = newTags["task_collect_ontology"][0].Id
	}
	if _, ok := newTags["task_end_kind"]; ok && len(newTags["task_end_kind"]) > 0 {
		taskEndKindId = newTags["task_end_kind"][0].Id
	}
	if _, ok := newTags["task_end_kind_right"]; ok && len(newTags["task_end_kind_right"]) > 0 {
		taskEndRightID = newTags["task_end_kind_right"][0].Id
	} else {
		taskEndRightID = taskEndKindId
	}

	pathMapping = []model.TaskPathMapping{}
	dirPathMapping = []model.TaskPathMapping{}
	URDFPath := syncDeviceType.Urdf
	URDFImage := syncDeviceType.Img
	if syncDeviceType.Urdf != "" {
		newURDF, err := utility.GenTaskPathMappings(ctx, syncDeviceType.Urdf)
		if err != nil {
			return 0, nil, nil, err
		}
		URDFPath = newURDF.Target
		newURDF.Origin = utility.RemoveLastPathSegment(newURDF.Origin)
		newURDF.Target = utility.RemoveLastPathSegment(newURDF.Target)
		dirPathMapping = append(dirPathMapping, newURDF)

	}

	if syncDeviceType.Img != "" {
		newImg, err := utility.GenTaskPathMappings(ctx, syncDeviceType.Img)
		if err != nil {
			return 0, nil, nil, err
		}
		URDFImage = newImg.Target
		pathMapping = append(pathMapping, newImg)

	}

	newDeviceType := entity.DeviceType{
		TenantId:            tenantId,
		TaskCollectOntology: taskCollectOntologyId,
		TaskEndKind:         taskEndKindId,
		TaskEndKindRight:    taskEndRightID,
		Urdf:                URDFPath,
		Img:                 URDFImage,
		ChannelConfig:       syncDeviceType.ChannelConfig,
		VisualConfig:        syncDeviceType.VisualConfig,
		Version:             syncDeviceType.Version,
		Name:                syncDeviceType.Name,
		IsLatest:            1,
	}
	id, err := dao.DeviceType.Ctx(ctx).InsertAndGetId(newDeviceType)
	if err != nil {
		return 0, nil, nil, err
	}
	return uint(id), pathMapping, dirPathMapping, nil
}

func genNewValues(origin string, idMap map[uint]uint) string {
	if origin == "" {
		return ""
	}
	// 解析原始字符串中的ID数组，格式如 [1843] 或 [1843,747]
	var originIDs []uint
	err := json.Unmarshal([]byte(origin), &originIDs)
	if err != nil {
		// 如果解析失败，返回原始字符串
		return origin
	}

	// 根据ID映射生成新的ID数组
	newIDs := make([]uint, 0, len(originIDs))
	for _, oldID := range originIDs {
		if newID, exists := idMap[oldID]; exists {
			newIDs = append(newIDs, newID)
		} else {
			// 如果映射中不存在该ID，保留原ID
			newIDs = append(newIDs, oldID)
		}
	}

	// 将新的ID数组序列化为字符串
	newValues, err := json.Marshal(newIDs)
	if err != nil {
		// 如果序列化失败，返回原始字符串
		return origin
	}

	return string(newValues)
}

func getIdFromValues(origin string) []uint {
	// 解析原始字符串中的ID数组，格式如 [1843] 或 [1843,747]
	var IDs []uint
	err := json.Unmarshal([]byte(origin), &IDs)
	if err != nil {
		// 如果解析失败，返回原始字符串
		return []uint{}
	}
	return IDs
}

// ensureTagExists 递归查找和创建父标签
func ensureTagExists(ctx context.Context, tagInfo *model.SyncTag, parentTagDetail map[string]*model.SyncTag, tenantId int) (*entity.Tag, error) {
	// 查找当前标签是否已存在
	var tag entity.Tag
	err := dao.Tag.Ctx(ctx).
		Where(dao.Tag.Columns().TenantId, tenantId).
		Where(dao.Tag.Columns().Code, tagInfo.Code).
		Scan(&tag)
	if err == nil && tag.Id > 0 {
		return &tag, nil
	}

	// 递归查找/创建父标签
	parentId := 0
	if tagInfo.Pid != 0 {
		parentSyncTag := parentTagDetail[gconv.String(tagInfo.Pid)]
		if parentSyncTag != nil {
			parentTag, err := ensureTagExists(ctx, parentSyncTag, parentTagDetail, tenantId)
			if err != nil {
				return nil, err
			}
			parentId = int(parentTag.Id)
		}
	}

	// 创建当前标签
	newTag := entity.Tag{
		TenantId: uint(tenantId),
		Code:     tagInfo.Code,
		Label:    tagInfo.Label,
		Pid:      parentId,
		Level:    tagInfo.Level,
		Status:   tagInfo.Status,
	}
	id, err := dao.Tag.Ctx(ctx).InsertAndGetId(&newTag)
	if err != nil {
		return nil, err
	}
	newTag.Id = uint(id)
	return &newTag, nil
}
