package review

import (
	"aim-data/internal/codes"
	"aim-data/internal/consts"
	"aim-data/internal/dao"
	"aim-data/internal/model"
	"aim-data/internal/model/do"
	"aim-data/internal/model/entity"
	"aim-data/internal/pkg/iam"
	"aim-data/internal/pkg/oss"
	redis2 "aim-data/internal/pkg/redis"
	"aim-data/internal/service"
	"aim-data/utility"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"path"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"

	"github.com/gogf/gf/v2/os/gtime"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type sReview struct{}

func New() *sReview {
	return &sReview{}
}

func init() {
	service.RegisterReview(New())
}

func (s *sReview) GetReviewData(ctx context.Context, episodeID uint) (*model.GetReviewOutput, error) {
	out := &model.GetReviewOutput{}
	episode, err := dao.Episode.GetByID(ctx, episodeID)
	if err != nil {
		g.Log().Warning(ctx, err)
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, gerror.New("采集记录查询出错"))
	}
	if episode.ReviewPath == "" {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, gerror.New("审核数据未生成"))
	}

	rawPath, framedPath, err := utility.GenPath(ctx, episode.Path)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeListEpisodeFailed, err)
	}
	if episode.FramedPath == "" {
		out.FramedPath = framedPath
	} else {
		out.FramedPath = episode.FramedPath
	}
	if episode.RawPath == "" {
		out.RawPath = rawPath
	} else {
		out.RawPath = episode.RawPath
	}
	out.SnCode = episode.SnCode
	out.Text = episode.Text
	out.DataType = uint(episode.DataType)

	job, err := dao.Job.GetByID(ctx, episode.JobId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, gerror.New("job查询出错"))
	}
	out.ID = episodeID
	out.Duration = job.Duration
	out.JobID = episode.JobId
	detail, err := service.Collect().GetActionStepDetail(ctx, *job)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, err)
	}
	out.ActionStep = detail

	task, err := dao.Task.GetByID(ctx, episode.TaskId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, gerror.New("task查询出错"))
	}
	//out.TaskContent = task.Content
	out.Config = task.Config

	jobTemp, err := dao.JobTemplate.GetByTaskID(ctx, episode.TaskId)
	if err != nil {
		g.Log().Warningf(ctx, "action_step_type使用默认值，job_template查询出错: %v", err)
		out.ActionStepType = consts.DefaultActionStepType
	} else {
		out.ActionStepType = jobTemp.ActionStepType
	}

	assignment, err := dao.Assignment.GetByID(ctx, episode.AssignmentId)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, err)
	}
	out.Collector = assignment.Username
	usr, err := iam.Client(ctx).GetUserByName(assignment.Username)
	if usr == nil {
		g.Log().Errorf(ctx, "not found user by name[%s], %v", assignment.Username, err)
	} else {
		out.CollectorName = usr.DisplayName
	}

	//tos://openloong-review-data/GENIE/3/8/a0:36:9f:76:12:8d/32
	// 查审核数据
	bucket, key, err := utility.S3PathParse(episode.ReviewPath)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, err)
	}

	// 判断目录是否符合要求
	svc := oss.Svc(ctx, oss.WithTenantTargetBucket(ctx, consts.ReviewBucketName))
	jsonKeys, isHasSubDir, isHasState, isHasMeta, err := isValidReviewKey(svc, bucket, key)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, err)
	}

	if !isHasSubDir {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, gerror.New("审核数据不存在"))
	}
	// state.json
	if !isHasState {
		g.Log(consts.CliLoggerName).Warning(ctx, "state.json not found")
	} else {
		download, err := oss.PSSvc(ctx, oss.WithTenantTargetBucket(ctx, consts.ReviewBucketName)).PreSignedDownload(episode.ReviewPath+"/state.json", consts.DefaultExpiredTime)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetReviewFailed, gerror.New("审核数据不存在"))
		}
		out.StateJson = download.SignedUrl
	}

	if !isHasMeta {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, gerror.New("Meta.json不存在"))
	}

	//meta.json
	metaJsonPrefix := fmt.Sprintf("%s/meta.json", key)
	data, err := svc.DownloadData(utility.S3Path(bucket, metaJsonPrefix))
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, err)
	}
	err = readJobFromReader(data, out)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, err)
	}

	//parts
	var parts []string
	for _, name := range jsonKeys {
		download, err := oss.PSSvc(ctx, oss.WithTenantTargetBucket(ctx, consts.ReviewBucketName)).PreSignedDownload(episode.ReviewPath+"/"+name, consts.DefaultExpiredTime)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetReviewFailed, err)
		}
		parts = append(parts, download.SignedUrl)
	}

	sort.Slice(parts, func(i, j int) bool {
		num1 := extractNumber(parts[i])
		num2 := extractNumber(parts[j])
		return num1 < num2
	})
	out.Parts = parts

	// 将审核数据放进redis里,防止其他人重复获取
	err = g.Redis().SetEX(ctx, fmt.Sprintf("%s:%d",
		fmt.Sprintf("%s:%d", consts.ReviewDataKey, out.JobID), episodeID), episodeID, consts.ReviewDataTTL)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetReviewFailed, err)
	}

	// 查询 device_type
	deviceType, err := service.Device().GetDeviceTypeByEpisode(ctx, episodeID, episode.TaskId, task.DeviceTypeId)
	if err != nil {
		g.Log().Warningf(ctx, "获取设备类型URDF失败，%v", err)
		return out, nil
	}
	if deviceType != nil {
		out.Urdf = service.Oss().PreSignWithFullS3Url(ctx, deviceType.Urdf)
		out.VisualConfig = deviceType.VisualConfig
		out.STLFiles, err = getURDFSubFiles(ctx, deviceType.Urdf)
		if err != nil {
			g.Log().Warningf(ctx, "获取设备类型STL文件失败，%v", err)
		}
	}
	return out, nil
}

func getURDFSubFiles(ctx context.Context, urdfPath string) ([]string, error) {
	s3Path := utility.ConvertToS3URI(urdfPath, utility.GetPathStype(ctx))
	bucket, key, err := utility.S3PathParseWithPresignUrl(s3Path)
	if err != nil {
		return []string{}, gerror.New(err.Error())
	}
	svc := oss.Svc(ctx, oss.WithTenantTargetBucket(ctx, consts.BaseBucketName))
	objects, err := svc.ListObject(bucket, path.Dir(key), "")
	if err != nil {
		return []string{}, gerror.New(err.Error())
	}
	var stlFiles []string
	for _, object := range objects {
		if strings.HasSuffix(object.Prefix, ".urdf") {
			continue
		}
		stlFiles = append(stlFiles, service.Oss().PreSignWithFullS3Url(ctx, bucket+"/"+object.Prefix))
	}
	return stlFiles, nil
}

func (s *sReview) GetNextReviewData(ctx context.Context, JobID uint, assignmentID uint) (uint, error) {
	// 查询是否有符合要求的episode 数据
	key := fmt.Sprintf("%s:%d", consts.ReviewDataKey, JobID)
	redisSvc, err := redis2.Svc(ctx)
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeGetNextReviewFailed, err)
	}
	get, err := redisSvc.ScanRedisKeys(fmt.Sprintf("%s:*", key), 10)
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeGetNextReviewFailed, err)
	}
	var existReviewData []int
	for _, item := range get {
		arr := strings.Split(item, ":")
		existReviewDataID, err := strconv.Atoi(arr[len(arr)-1])
		if err != nil {
			return 0, gerror.WrapCode(codes.CodeGetNextReviewFailed, err)
		}
		existReviewData = append(existReviewData, existReviewDataID)
	}
	type EpisodeID struct {
		ID uint64 `json:"id"`
	}
	m := dao.Episode.Ctx(ctx)
	cond := m.Builder()
	if assignmentID > 0 {
		cond = cond.Where("assignment_id", assignmentID)
	}
	cond = cond.Where(do.Episode{
		JobId: JobID,
	})
	cond = cond.Where(m.Builder().WhereNotNull("review_path").WhereNot("review_path", ""))

	// 首先尝试获取未审核的数据
	notCheckCond := cond.Clone()
	notCheckCond = notCheckCond.Where(do.Episode{
		Status: consts.EpisodeStatusNotCheck,
		JobId:  JobID,
	})
	if len(existReviewData) > 0 {
		notCheckCond = notCheckCond.WhereNotIn("id", existReviewData)
	}

	record, err := dao.Episode.Ctx(ctx).Fields(dao.Episode.Columns().Id).
		OrderAsc(dao.Episode.Columns().CreatedAt).
		Where(notCheckCond).One()

	// 如果没有未审核的数据，则使用新的逻辑
	if record == nil {
		if len(existReviewData) > 0 {
			cond = cond.WhereNotIn("id", existReviewData)
		}

		record, err = dao.Episode.Ctx(ctx).Fields(dao.Episode.Columns().Id).
			OrderAsc(dao.Episode.Columns().CreatedAt).
			WhereIn(dao.Episode.Columns().Status, consts.GetEpisodeStatus(consts.EpisodeStatusTypeCollected)).
			Where(cond).One()
	}

	var episodeID EpisodeID
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeGetNextReviewFailed, err)
	}
	if record == nil {
		return 0, gerror.WrapCode(codes.CodeGetNextReviewFailed, gerror.New("该采集员数据已审核完成，请返回列表继续审核"))
	}
	err = record.Struct(&episodeID)
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeGetNextReviewFailed, err)
	}

	err = g.Redis().SetEX(ctx, fmt.Sprintf("%s:%d", key, episodeID.ID), episodeID, consts.ReviewDataTTL)
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeGetNextReviewFailed, gerror.New("该采集员数据已审核完成，请返回列表继续审核"))
	}
	return uint(episodeID.ID), nil
}

func isJSON(filename string) bool {
	// 使用 filepath.Ext 获取文件后缀
	ext := filepath.Ext(filename)
	return strings.ToLower(ext) == ".json"
}

// hasSubdirectories 列出指定前缀下的对象，并判断是否存在子目录
func isValidReviewKey(svc oss.ISvc, bucketName, prefix string) (names []string, isHasSubDir, isHasState, isHasMeta bool, err error) {
	// 使用 ListObjectsV2 迭代列出对象
	if !strings.HasSuffix(prefix, "/") {
		prefix += "/"
	}
	//objects, err := svc.ListAllObjects(ctx, bucketName, prefix)
	objects, err := svc.ListObject(bucketName, prefix, "/")
	if err != nil {
		return nil, false, false, false, err
	}
	for _, object := range objects {
		if !strings.Contains(object.Prefix, "meta.json") && !strings.Contains(object.Prefix, "state.json") {
			if isJSON(object.Prefix) {
				names = append(names, object.Name)
			}
		}
		if strings.Contains(object.Prefix, "meta.json") {
			isHasMeta = true
		}
		if strings.Contains(object.Prefix, "state.json") {
			isHasState = true
		}

	}
	if len(objects) >= 1 {
		isHasSubDir = true
	}
	return names, isHasSubDir, isHasState, isHasMeta, nil

}

// 提取URL中的数字部分
func extractNumber(url string) int {
	parts := strings.Split(url, "/")
	parts = strings.Split(parts[len(parts)-1], "?")
	numStr := strings.TrimSuffix(parts[0], ".json")
	num, _ := strconv.Atoi(numStr)
	return num
}

func readJobFromReader(reader io.ReadCloser, out *model.GetReviewOutput) error {
	defer func(reader io.ReadCloser) {
		err := reader.Close()
		if err != nil {
		}
	}(reader)
	decoder := json.NewDecoder(reader)
	if err := decoder.Decode(out); err != nil {
		return err
	}
	return nil
}

func (s *sReview) AddReview(ctx context.Context, input model.AddReviewInput) (uint, error) {
	episode, err := service.Collect().GetEpisode(ctx, input.EpisodeID)
	if err != nil {
		g.Log().Warningf(ctx, "get episode[%d] failed with %v", input.EpisodeID, err)
		return 0, gerror.WrapCode(codes.CodeAddReviewFailed, gerror.New("获取采集记录失败"))
	}

	// 只有在白名单才能提交
	userID := utility.GetAccessUserId(ctx)
	isCanAssign := service.Collect().IsCollectorInWhiteList(ctx, input.TaskID, userID, uint(consts.TaskUserRoleChecker))
	if !isCanAssign {
		return 0, gerror.WrapCode(codes.CodeAddReviewFailed, gerror.New("不在任务白名单，无法审核"))
	}

	// 校验任务状态是否是运营中
	var task entity.Task
	err = dao.Task.Ctx(ctx).Where(do.Task{Id: episode.TaskId}).Scan(&task)
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeAddReviewFailed, err)
	}
	if task.Status != consts.TaskStatusOperational {
		return 0, gerror.WrapCode(codes.CodeAddReviewFailed, gerror.New("任务不是处于运营中状态，无法审核"))
	}

	input.TenantID = utility.GetAccessTenantId(ctx)
	input.TaskID = episode.TaskId
	input.JobID = episode.JobId
	input.UserID = utility.GetAccessUserId(ctx)
	input.Username = utility.GetAccessUsername(ctx)
	// 检查review是否有变更，无变更，不提交更新
	rev, _ := s.GetReview(ctx, input.EpisodeID)
	if rev != nil {
		if rev.Status == input.Status &&
			rev.Comment == input.Comment &&
			rev.KeyFrame == input.KeyFrame &&
			rev.ErrorCountScore == input.ErrorCountScore &&
			rev.SmoothnessScore == input.SmoothnessScore &&
			rev.OverallScore == input.OverallScore &&
			rev.UsabilityScore == input.UsabilityScore &&
			rev.ActionStepCalibration == input.ActionStepCalibration {
			// 无需更新审核员信息
			return 0, nil
		}
	}

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		switch input.Status {
		case consts.EpisodeStatusCheckPass, consts.EpisodeStatusCheckNotPass, consts.EpisodeStatusInvalidByManual, consts.EpisodeStatusInvalid:
		default:
			return gerror.Newf("invalid status %d", input.Status)
		}
		_, err = tx.Model(dao.Review.Table()).Save(input)
		if err != nil {
			return err
		}
		if input.Status == consts.EpisodeStatusInvalid {
			input.Status = consts.EpisodeStatusInvalidByManual
		}
		if utility.HasExists([]uint{consts.EpisodeStatusCheckPass, consts.EpisodeStatusCheckNotPass}, uint(episode.Status)) &&
			!utility.HasExists([]uint{consts.EpisodeStatusCheckPass, consts.EpisodeStatusCheckNotPass, consts.EpisodeStatusInvalid, consts.EpisodeStatusInvalidByManual}, uint(input.Status)) {
			input.Status = 0
		}
		episodeUpdate := &do.Episode{
			UpdatedAt: gtime.Now(),
		}
		if input.Status != rev.Status {
			// 审核状态有变更，需同步更新episode状态，否则无需更新
			episodeUpdate.Status = input.Status
		}
		_, err = tx.Model(dao.Episode.Table()).Where("id", input.EpisodeID).Data(episodeUpdate).OmitEmpty().Update()
		return err
	})
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeAddReviewFailed, err)
	}
	return 0, nil
}

func (s *sReview) GetReview(ctx context.Context, episodeID uint) (*entity.Review, error) {
	var r entity.Review
	record, err := dao.Review.Ctx(ctx).Where(do.Review{EpisodeId: episodeID}).OrderDesc("updated_at").One()
	if err != nil {
		g.Log().Warningf(ctx, "采集记录[%d]未找到标注结果, %v", episodeID, err)
		return nil, gerror.WrapCode(codes.CodeGetReviewLabelFailed.
			WithViewMsgf(ctx, "采集记录[%d]未找到标注结果", episodeID),
			gerror.Newf("采集记录[%d]未找到标注结果", episodeID))
	}
	if record.IsEmpty() {
		return &r, nil
	}
	err = record.Struct(&r)
	if err != nil {
		g.Log().Warningf(ctx, "采集记录[%d]标注结果解析失败, %v", episodeID, err)
		return nil, gerror.WrapCode(codes.CodeGetReviewLabelFailed.
			WithViewMsgf(ctx, "采集记录[%d]标注结果解析失败", episodeID),
			gerror.Newf("采集记录[%d]标注结果解析失败", episodeID))
	}
	//transReview(ctx, &r)
	return &r, nil
}

func transReview(ctx context.Context, reviewRecord *entity.Review) {
	commonFrames := make([]model.CommonFrameAnnotate, 0)
	if err := json.Unmarshal([]byte(reviewRecord.KeyFrame), &commonFrames); err != nil {
		g.Log().Infof(ctx, "trans review frame failed:%v", err)
		return
	}
	commonFrames = utility.TransformFrameAnnotate(commonFrames)
	// 将转换后的数据重新序列化
	newKeyFrame, err := json.Marshal(commonFrames)
	if err != nil {
		g.Log().Infof(ctx, "trans review frame failed:%v", err)
		return
	}
	reviewRecord.KeyFrame = string(newKeyFrame)
}

func (s *sReview) ListDeliverDetail(ctx context.Context, jobID uint) ([]*model.DeliverDetailOutput, error) {
	type QueryEpisode struct {
		AssignmentID uint `json:"job_id"`
		Status       uint `json:"status"`
		Count        int  `json:"count"`
	}
	var queryEpisode []QueryEpisode
	err := dao.Episode.Ctx(ctx).Where(dao.Episode.Columns().JobId, jobID).
		WhereIn(dao.Episode.Columns().Status,
			[]uint{
				consts.EpisodeStatusNotCheck,
				consts.EpisodeStatusCheckPass,
				consts.EpisodeStatusCheckNotPass,
				consts.EpisodeStatusSystemCheckPass,
			},
		).Fields(
		"assignment_id", "status", "count(0) as count",
	).
		Group(dao.Episode.Columns().AssignmentId, dao.Episode.Columns().Status).Scan(&queryEpisode)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetGetDeliverDetailFailed, err)
	}

	mapRes := make(map[uint]*model.DeliverDetailOutput)
	var res []*model.DeliverDetailOutput
	var assignmentIDs []uint

	for _, item := range queryEpisode {
		assignmentIDs = append(assignmentIDs, item.AssignmentID)
		value, exists := mapRes[item.AssignmentID]
		if !exists {
			value = &model.DeliverDetailOutput{
				AssignmentId: item.AssignmentID,
			}
		}
		switch item.Status {
		case consts.EpisodeStatusNotCheck:
			value.NotCheck = item.Count
		case consts.EpisodeStatusCheckPass:
			value.ManualCheckPass = item.Count
		case consts.EpisodeStatusCheckNotPass:
			value.ManualCheckNotPass = item.Count
		case consts.EpisodeStatusSystemCheckPass:
			value.SystemCheckPass = item.Count
		}
		mapRes[item.AssignmentID] = value
	}

	type QueryAssignment struct {
		ID       uint   `json:"id"`
		UserID   string `json:"user_id"`
		Username string `json:"username"`
	}

	assignmentMap := make(map[uint]QueryAssignment)
	var queryAssignment []QueryAssignment
	err = dao.Assignment.Ctx(ctx).WhereIn(dao.Assignment.Columns().Id, assignmentIDs).Fields(
		dao.Assignment.Columns().Id,
		dao.Assignment.Columns().UserId,
		dao.Assignment.Columns().Username,
	).Scan(&queryAssignment)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetGetDeliverDetailFailed, err)
	}
	for _, assignment := range queryAssignment {
		assignmentMap[assignment.ID] = assignment
	}

	for _, value := range mapRes {
		value.UserID = assignmentMap[value.AssignmentId].UserID
		value.Username = assignmentMap[value.AssignmentId].Username
		value.ManualChecked = value.ManualCheckPass + value.ManualCheckNotPass
		if float64(value.NotCheck)+
			float64(value.ManualCheckPass)+float64(value.SystemCheckPass)+float64(value.ManualCheckNotPass) > 0 {
			value.CheckPer = math.Round(float64(value.ManualChecked)/(float64(value.NotCheck)+
				float64(value.ManualCheckPass)+float64(value.SystemCheckPass)+float64(value.ManualCheckNotPass))*100) / 100
		}
		if value.ManualChecked > 0 {
			value.CheckPassPer = math.Round(float64(value.ManualCheckPass)/float64(value.ManualChecked)*100) / 100
		}
	}

	for _, value := range mapRes {
		res = append(res, value)
	}

	var wg sync.WaitGroup
	for i, item := range res {
		wg.Add(1)
		go processDeliverDetailOutput(ctx, i, item, res, &wg)
	}
	wg.Wait()

	sort.Sort(ByCheckPassPer(res))
	return res, nil
}

type ByCheckPassPer []*model.DeliverDetailOutput

func (a ByCheckPassPer) Len() int      { return len(a) }
func (a ByCheckPassPer) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a ByCheckPassPer) Less(i, j int) bool {
	if a[i].CheckPassPer > a[j].CheckPassPer {
		return true
	} else if a[i].CheckPassPer == a[j].CheckPassPer {
		return a[i].AssignmentId > a[j].AssignmentId
	} else {
		return false
	}
} // 从大到小排序

func processDeliverDetailOutput(ctx context.Context, i int, item *model.DeliverDetailOutput, list []*model.DeliverDetailOutput, wg *sync.WaitGroup) {
	defer wg.Done()
	usr, err := iam.Client(ctx).GetUserByName(item.Username)
	if err != nil || usr == nil {
		fmt.Printf("Not found username %s: %v\n", item.Username, err)
		return
	}
	list[i].UserID = usr.Id
	list[i].Collector = usr.DisplayName
}

func (s *sReview) OneClickDeliver(ctx context.Context, assignmentIDs []uint) (count int64, err error) {
	affected, err := dao.Episode.Ctx(ctx).Data(g.Map{
		dao.Episode.Columns().Status: consts.EpisodeStatusSystemCheckPass,
	}).WhereIn(dao.Episode.Columns().AssignmentId, assignmentIDs).
		Where(dao.Episode.Columns().Status, consts.EpisodeStatusNotCheck).
		Where(do.Episode{DataType: consts.EpisodeDataTypeNormal}).
		UpdateAndGetAffected()
	if err != nil {
		return 0, gerror.WrapCode(codes.CodeOneClickDeliverFailed, err)
	}
	return affected, nil
}

func (s *sReview) ListReviewStatusCount(ctx context.Context, in model.ListReviewStatusCountInput) (*model.ListReviewStatusCountOutput, error) {
	var (
		out model.ListReviewStatusCountOutput
		err error
	)

	// 如果有审核员查询条件
	var queryEpisodeIDs []uint
	if in.Checker != "" {
		type QueryReview struct {
			EpisodeID uint `json:"episode_id"`
		}
		var queryReviews []QueryReview
		err = dao.Review.Ctx(ctx).Where(do.Review{UserId: in.Checker, JobId: in.JobID}).
			Fields(dao.Review.Columns().EpisodeId).Scan(&queryReviews)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeListEpisodeFailed, err)
		}
		for _, item := range queryReviews {
			queryEpisodeIDs = append(queryEpisodeIDs, item.EpisodeID)
		}
	}

	// 分页查episode
	cond := dao.Episode.Ctx(ctx).Builder().Where(do.Episode{
		JobId: in.JobID,
	})
	if in.DataType != 0 {
		cond = cond.Where(dao.Episode.Columns().DataType, in.DataType)
	}
	if in.EpisodeID != 0 {
		// 直接查询 episode
		cond = cond.Where(dao.Episode.Columns().Id, in.EpisodeID)
	} else {
		if len(in.AssignmentIDs) > 0 {
			// 通过采集员查询
			cond = cond.WhereIn(dao.Episode.Columns().AssignmentId, in.AssignmentIDs)
		}
	}
	if len(queryEpisodeIDs) >= 1 {
		cond = cond.WhereIn(dao.Episode.Columns().Id, queryEpisodeIDs)
	}

	// 各个状态的数量
	type QueryStatusCount struct {
		Status uint `json:"status"`
		Count  int  `json:"count"`
	}
	var statusCountRes model.StatusCountDetail
	var queryStatusCount []QueryStatusCount
	err = dao.Episode.Ctx(ctx).Where(cond).Fields("status", "count(0)  as count").Group(dao.Episode.Columns().Status).Scan(&queryStatusCount)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeListEpisodeFailed, err)
	}
	for _, item := range queryStatusCount {
		statusCountRes.All += item.Count
		switch item.Status {
		case consts.EpisodeStatusNotUpload:
			statusCountRes.NotUpload += item.Count
		case consts.EpisodeStatusUploading:
			statusCountRes.Uploading += item.Count
		case consts.EpisodeStatusUploaded:
			statusCountRes.Uploaded += item.Count
		case consts.EpisodeStatusUploadFailed:
			statusCountRes.UploadFailed += item.Count
		case consts.EpisodeStatusCheckPass:
			statusCountRes.CheckPassManual += item.Count
		case consts.EpisodeStatusCheckNotPass:
			statusCountRes.CheckNotPass += item.Count
		case consts.EpisodeStatusInvalid:
			statusCountRes.PPInvalid += item.Count
		case consts.EpisodeStatusProcessing:
			statusCountRes.Processing += item.Count
		case consts.EpisodeStatusProcessFail:
			statusCountRes.ProcessFail += item.Count
		case consts.EpisodeStatusNotCheck:
			statusCountRes.NotCheck += item.Count
		case consts.EpisodeStatusInvalidByManual:
			statusCountRes.CheckInvalid += item.Count

		}
	}
	out.StatusCount = statusCountRes
	return &out, nil
}
func (s *sReview) ListNextEpisode(ctx context.Context, req *model.NextEpisodeInput) (*model.NextEpisodeOutput, error) {
	// 获取当前episode信息
	currentEpisode, err := dao.Episode.GetByID(ctx, req.EpisodeID)
	if err != nil || currentEpisode == nil {
		return nil, gerror.WrapCode(codes.CodeGetNextEpisodeFailed, gerror.New("当前记录不存在"))
	}

	// 构建基础查询条件
	cond := dao.Episode.Ctx(ctx).
		Handler(dao.WithTenantID()).
		Where(do.Episode{
			JobId: currentEpisode.JobId,
		})

	// 1. status为切片
	if len(req.Status) > 0 {
		cond = cond.WhereIn(dao.Episode.Columns().Status, req.Status)
	}
	// 2. assignmentid为assignment_id
	if req.AssignmentID > 0 {
		cond = cond.Where(dao.Episode.Columns().AssignmentId, req.AssignmentID)
	}
	// 3. datatype直接筛选
	if req.DataType != "" {
		cond = cond.Where(dao.Episode.Columns().DataType, req.DataType)
	}
	// 4. checker为review表user_id，联表
	if req.Checker != "" {
		cond = cond.LeftJoin("review", "r", "r.episode_id=episode.id").
			Where("r.user_id", req.Checker)
	}

	// 获取下一条记录ID
	val, err := cond.
		OrderDesc("episode."+dao.Episode.Columns().AssignmentId).
		OrderAsc("episode."+dao.Episode.Columns().CreatedAt).
		OrderAsc("episode."+dao.Episode.Columns().Id).
		Where(
			"(episode.assignment_id = ? AND episode.id > ?) OR (episode.assignment_id < ?)",
			currentEpisode.AssignmentId, currentEpisode.Id, currentEpisode.AssignmentId,
		).
		Fields("DISTINCT episode." + dao.Episode.Columns().Id + " as id").
		Limit(1).
		Value()

	// 检查是否有查询错误
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetNextEpisodeFailed, err)
	}

	// 检查是否没有找到记录（已经是最后一条数据）
	if val == nil || val.IsEmpty() {
		return nil, gerror.WrapCode(codes.CodeGetLastEpisodeFailed, gerror.New("已经是最后一条数据"))
	}

	return &model.NextEpisodeOutput{EpisodeID: val.Uint()}, nil
}

func (s *sReview) ListAllReviewer(ctx context.Context, taskID uint) (*model.UserListOutput, error) {
	var output model.UserListOutput
	taskUsers, err := dao.TaskUser.Ctx(ctx).Handler(dao.WithTenantID()).
		Where(dao.TaskUser.Columns().TaskId, taskID).
		Where(dao.TaskUser.Columns().Role, consts.TaskUserRoleChecker).
		Where(dao.TaskUser.Columns().Status, consts.TaskUserStatusActive).
		All()
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeUserListFailed, err)
	}
	for _, taskUser := range taskUsers {
		username := taskUser["username"].String()
		user, err := iam.Client(ctx).GetUserByName(username)
		if err != nil || user == nil {
			continue
		}
		usr := model.UserDetail{
			UserId:          user.Id,
			Username:        user.Name,
			DisplayName:     user.DisplayName,
			Phone:           user.Phone,
			IsForbidden:     user.IsForbidden,
			CreatedTime:     user.CreatedTime,
			UpdatedTime:     user.UpdatedTime,
			LastSigningTime: user.LastSigninTime,
			Roles:           []string{},
			Source:          getSource(user),
		}
		tenantName := utility.GetAccessTenant(ctx)
		array, err := dao.UserRole.Ctx(ctx).Fields(dao.UserRole.Columns().RoleName).
			Where(dao.UserRole.Columns().UserName, user.Name).
			Where(dao.UserRole.Columns().TenantName, tenantName).Array()
		if err != nil {
			continue
		}
		roles := make([]string, len(array))
		for i, role := range array {
			roles[i] = role.String()
		}
		usr.Roles = roles
		output.List = append(output.List, usr)
	}
	output.Total = len(output.List)
	return &output, nil
}

func getSource(user *casdoorsdk.User) uint {
	if user.Lark != "" {
		return 1
	}
	return 0
}
