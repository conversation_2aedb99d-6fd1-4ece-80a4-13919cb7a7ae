{"items": [{"id": 12037875, "uuid": "51c029db-3cdb-4c74-a872-ed1318af853a", "job_id": 2810069, "task_id": 3324695, "worker_id": 1998, "uri": "zhoupu://aidea-sim/episodes/51c029db-3cdb-4c74-a872-ed1318af853a", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [1, -1], "fps": 30, "frame_count": 841, "task_name": "[genie_pass_water_0]", "task_status": false, "visualization": "simubotix"}, "status": 0, "is_success": 0, "created_at": "2025-06-02T10:20:11+08:00", "manual_check": null, "post_check": null}, {"id": 12037869, "uuid": "b93965ad-0a84-4385-b228-9c645d63655f", "job_id": 2810069, "task_id": 3324689, "worker_id": 1962, "uri": "zhoupu://aidea-sim/episodes/b93965ad-0a84-4385-b228-9c645d63655f", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [1, -1], "fps": 30, "frame_count": 947, "task_name": "[genie_pass_water_0]", "task_status": false, "visualization": "simubotix"}, "status": 0, "is_success": 0, "created_at": "2025-06-02T10:18:01+08:00", "manual_check": null, "post_check": null}, {"id": 12037866, "uuid": "e088c4fd-0185-45b4-81fc-6c9c516925eb", "job_id": 2810069, "task_id": 3324691, "worker_id": 2000, "uri": "zhoupu://aidea-sim/episodes/e088c4fd-0185-45b4-81fc-6c9c516925eb", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [1, -1], "fps": 30, "frame_count": 524, "task_name": "[genie_pass_water_0]", "task_status": false, "visualization": "simubotix"}, "status": 0, "is_success": 0, "created_at": "2025-06-02T10:14:36+08:00", "manual_check": null, "post_check": null}, {"id": 12037865, "uuid": "77a9a593-4b76-456b-9125-dacb30f242c3", "job_id": 2810069, "task_id": 3324693, "worker_id": 1960, "uri": "zhoupu://aidea-sim/episodes/77a9a593-4b76-456b-9125-dacb30f242c3", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [1, -1], "fps": 30, "frame_count": 281, "task_name": "[genie_pass_water_0]", "task_status": false, "visualization": "simubotix"}, "status": 0, "is_success": 0, "created_at": "2025-06-02T10:14:25+08:00", "manual_check": null, "post_check": null}, {"id": 12037864, "uuid": "f5376377-0b60-447c-ae0b-768a8496e392", "job_id": 2810069, "task_id": 3324692, "worker_id": 1968, "uri": "zhoupu://aidea-sim/episodes/f5376377-0b60-447c-ae0b-768a8496e392", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [1, -1], "fps": 30, "frame_count": 238, "task_name": "[genie_pass_water_0]", "task_status": false, "visualization": "simubotix"}, "status": 0, "is_success": 0, "created_at": "2025-06-02T10:14:19+08:00", "manual_check": null, "post_check": null}, {"id": 12037863, "uuid": "65ce66b1-3e04-4ce2-baa5-b8fdd3f61ffc", "job_id": 2810069, "task_id": 3324690, "worker_id": 2004, "uri": "zhoupu://aidea-sim/episodes/65ce66b1-3e04-4ce2-baa5-b8fdd3f61ffc", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [1, -1], "fps": 30, "frame_count": 524, "task_name": "[genie_pass_water_0]", "task_status": false, "visualization": "simubotix"}, "status": 0, "is_success": 0, "created_at": "2025-06-02T10:13:55+08:00", "manual_check": null, "post_check": null}, {"id": 12037862, "uuid": "31421d5e-4cb4-4eda-b06f-86565d29d1c6", "job_id": 2810069, "task_id": 3324684, "worker_id": 1970, "uri": "zhoupu://aidea-sim/episodes/31421d5e-4cb4-4eda-b06f-86565d29d1c6", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [0, -1], "fps": 30, "frame_count": 668, "task_name": "[genie_pass_water_0]", "task_status": false, "visualization": "simubotix"}, "status": 0, "is_success": 0, "created_at": "2025-06-02T10:13:40+08:00", "manual_check": null, "post_check": null}, {"id": 12037860, "uuid": "b872a74a-e911-4e13-8036-dea85ce41bea", "job_id": 2810069, "task_id": 3324687, "worker_id": 1958, "uri": "zhoupu://aidea-sim/episodes/b872a74a-e911-4e13-8036-dea85ce41bea", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [-1, -1], "fps": 30, "frame_count": 540, "task_name": "[genie_pass_water_0]", "task_status": true, "visualization": "simubotix"}, "status": 0, "is_success": 1, "created_at": "2025-06-02T10:12:44+08:00", "manual_check": null, "post_check": null}, {"id": 12037859, "uuid": "089aeb77-d1d8-4ee4-b3f6-f27e72432422", "job_id": 2810069, "task_id": 3324686, "worker_id": 1966, "uri": "zhoupu://aidea-sim/episodes/089aeb77-d1d8-4ee4-b3f6-f27e72432422", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [-1, -1], "fps": 30, "frame_count": 500, "task_name": "[genie_pass_water_0]", "task_status": true, "visualization": "simubotix"}, "status": 0, "is_success": 1, "created_at": "2025-06-02T10:12:24+08:00", "manual_check": null, "post_check": null}, {"id": 12037858, "uuid": "c809a696-20b2-4db0-a76b-22f356a42ef0", "job_id": 2810069, "task_id": 3324683, "worker_id": 1992, "uri": "zhoupu://aidea-sim/episodes/c809a696-20b2-4db0-a76b-22f356a42ef0", "summary": {"camera_info": {"hand_left": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_left_depth.png", "rgb": "camera/{frame_num}/hand_left_color.jpg", "video": "hand_left_color.mp4"}}, "hand_right": {"intrinsic": {"fx": 435.2614052522416, "fy": 435.2614052522416, "height": 480, "ppx": 424, "ppy": 240, "width": 848}, "output": {"depth": "camera/{frame_num}/hand_right_depth.png", "rgb": "camera/{frame_num}/hand_right_color.jpg", "video": "hand_right_color.mp4"}}, "head": {"intrinsic": {"fx": 634.0862399675711, "fy": 634.0862399675711, "height": 720, "ppx": 640, "ppy": 360, "width": 1280}, "output": {"depth": "camera/{frame_num}/head_depth.png", "rgb": "camera/{frame_num}/head_color.jpg", "video": "head_color.mp4"}}}, "fail_stage_step": [1, -1], "fps": 30, "frame_count": 463, "task_name": "[genie_pass_water_0]", "task_status": false, "visualization": "simubotix"}, "status": 0, "is_success": 0, "created_at": "2025-06-02T10:11:24+08:00", "manual_check": null, "post_check": null}], "page": 1, "per_page": 10, "total": 2, "total_pages": 2}