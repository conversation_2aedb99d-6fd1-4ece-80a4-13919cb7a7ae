package collect

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"aim-data/internal/codes"
	"aim-data/internal/consts"
	"aim-data/internal/dao"
	"aim-data/internal/model"
	"aim-data/internal/model/do"
	"aim-data/internal/model/entity"
	"aim-data/internal/pkg/iam"
	"aim-data/utility"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

// tenant-isolated
func (s *sCollect) GetStatInfo(ctx context.Context, in model.GetStatInfoInput) (*model.GetStatInfoOutput, error) {
	cacheKey := "stat_info"
	if in.TaskID != 0 {
		cacheKey = fmt.Sprintf("%s:task_%d", cacheKey, in.TaskID)
	}
	if in.JobID != 0 {
		cacheKey = fmt.Sprintf("%s:job_%d", cacheKey, in.JobID)
	}

	// 先尝试从Redis缓存获取数据
	v, getErr := g.Redis().Get(ctx, cacheKey)
	if getErr == nil && !v.IsEmpty() {
		var cacheData model.GetStatInfoOutput
		var scanErr error
		if scanErr = v.Scan(&cacheData); scanErr == nil {
			return &cacheData, nil
		}
		g.Log().Warningf(ctx, "Failed to parse cached data for key %s: %v", cacheKey, scanErr)
	}

	// 缓存未命中或解析失败，查询数据库
	return GetStatInfoInDB(ctx, in)
}

func GetStatInfoInDB(ctx context.Context, in model.GetStatInfoInput) (*model.GetStatInfoOutput, error) {
	tenantID := utility.GetAccessTenantId(ctx)

	// 构建SQL查询
	sqlQuery := `
	WITH task_id_filter AS (
		SELECT task.id as task_filter_id
		FROM task
		JOIN tag_rel ON tag_rel.resource_type = 'task' 
			AND tag_rel.resource_id = task.id
		JOIN tag ON tag.id = tag_rel.tag_id 
			AND tag.label = '正式采集'
			AND tag.tenant_id = ?
		WHERE task.deleted_at = 0 
			AND task.tenant_id = ?
			%s
	),
	episode_base AS (
		SELECT 
			e.*,
			IFNULL(JSON_LENGTH(jt.action_step), 1) as step_count,
			a.user_id as collector_user_id
		FROM episode e
		RIGHT JOIN task_id_filter t ON e.task_id = t.task_filter_id
		RIGHT JOIN job j ON e.job_id = j.id AND j.deleted_at = 0
		LEFT JOIN job_template jt ON e.task_id = jt.task_id AND jt.deleted_at = 0
		LEFT JOIN assignment a ON a.id = e.assignment_id AND a.deleted_at = 0
		WHERE e.deleted_at = 0
	)
	SELECT 
		(SELECT COUNT(DISTINCT task_filter_id) FROM task_id_filter) as task_count,
		COUNT(DISTINCT id) as episode_count,
		SUM(step_count) as step_count,
		SUM(CASE WHEN status IN (6, 7,10,12) THEN step_count ELSE 0 END) as step_check_count,
		COUNT(DISTINCT CASE WHEN status IN (6, 7,10,12) THEN id END) as episode_check_count,
		SUM(CASE WHEN status IN (6, 10) THEN step_count ELSE 0 END) as step_check_passed_count,
		COUNT(DISTINCT CASE WHEN status IN (6, 10) THEN id END) as episode_check_passed_count,
		SUM(duration)/1000/3600 as collected_duration,
		COUNT(DISTINCT sn_code) as device_count,
		SUM(size) / 1024 / 1024 / 1024 as total_size,
		SUM(CASE WHEN status IN (6,7,12,9,99) THEN 1 ELSE 0 END) * 1.0 / 
			NULLIF(SUM(CASE WHEN status NOT IN (1,2,3) THEN 1 ELSE 0 END), 0) as post_process_success_rate,
		SUM(CASE WHEN status IN (6,10) THEN 1 ELSE 0 END) * 1.0 / 
			NULLIF(SUM(CASE WHEN status IN (6, 7, 12, 10) THEN 1 ELSE 0 END), 0) as review_pass_rate,
		COUNT(DISTINCT CASE WHEN status IN (5, 9) THEN id END) as pending_review_backlog,
		COUNT(DISTINCT CASE WHEN status = 8 THEN id END) as post_processing_backlog,
		COUNT(DISTINCT CASE WHEN status IN (8, 3) THEN id END) as pending_processing_backlog,
		COUNT(DISTINCT CASE WHEN collector_user_id IS NOT NULL THEN collector_user_id END) as collector_count,
		(SUM(IF(status IN (6, 7, 12, 9, 99), 1, 0)) * 1.0 / NULLIF(SUM(IF(status NOT IN (1, 2, 3), 1, 0)), 0)) *
		(SUM(CASE WHEN status IN (6, 10) THEN 1 ELSE 0 END) * 1.0 / 
			NULLIF(SUM(CASE WHEN status IN (6, 10, 7, 12) THEN 1 ELSE 0 END), 0)) as full_process_pass_rate,
		COUNT(DISTINCT id) * 1.0 / 
			NULLIF(COUNT(DISTINCT CASE WHEN collector_user_id IS NOT NULL THEN collector_user_id END), 0) as avg_episode_count_per_person,
		SUM(duration)/1000/3600 * 1.0 / 
			NULLIF(COUNT(DISTINCT CASE WHEN collector_user_id IS NOT NULL THEN collector_user_id END), 0) as avg_collection_duration_per_person
	FROM episode_base`

	var args []interface{}
	var taskFilter string

	// 处理TaskID过滤
	if in.TaskID != 0 {
		taskFilter = "AND task.id = ?"
		args = []interface{}{tenantID, tenantID, in.TaskID}
	} else {
		taskFilter = ""
		args = []interface{}{tenantID, tenantID}
	}

	finalSQL := fmt.Sprintf(sqlQuery, taskFilter)

	// 执行查询
	var result struct {
		TaskCount                      int     `json:"task_count"`
		EpisodeCount                   int     `json:"episode_count"`
		StepCount                      int     `json:"step_count"`
		EpisodeCheckCount              int     `json:"episode_check_count"`
		StepCheckCount                 int     `json:"step_check_count"`
		StepCheckPassedCount           int     `json:"step_check_passed_count"`
		EpisodeCheckPassedCount        int     `json:"episode_check_passed_count"`
		CollectedDuration              float64 `json:"collected_duration"`
		DeviceCount                    int     `json:"device_count"`
		TotalSize                      float64 `json:"total_size"`
		PostProcessSuccessRate         float64 `json:"post_process_success_rate"`
		ReviewPassRate                 float64 `json:"review_pass_rate"`
		PendingReviewBacklog           int     `json:"pending_review_backlog"`
		PostProcessingBacklog          int     `json:"post_processing_backlog"`
		PendingProcessingBacklog       int     `json:"pending_processing_backlog"`
		CollectorCount                 int     `json:"collector_count"`
		FullProcessPassRate            float64 `json:"full_process_pass_rate"`
		AvgEpisodeCountPerPerson       float64 `json:"avg_episode_count_per_person"`
		AvgCollectionDurationPerPerson float64 `json:"avg_collection_duration_per_person"`
	}

	err := g.DB().GetScan(ctx, &result, finalSQL, args...)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatInfoFailed, err)
	}

	// 构建返回结果
	out := &model.GetStatInfoOutput{
		TaskCount:                      result.TaskCount,
		EpisodeCount:                   result.EpisodeCount,
		StepCount:                      result.StepCount,
		EpisodeCheckCount:              result.EpisodeCheckCount,
		StepCheckCount:                 result.StepCheckCount,
		StepCheckPassedCount:           result.StepCheckPassedCount,
		EpisodeCheckPassedCount:        result.EpisodeCheckPassedCount,
		CollectedDuration:              result.CollectedDuration,
		DeviceCount:                    result.DeviceCount,
		TotalSize:                      result.TotalSize,
		PostProcessSuccessRate:         result.PostProcessSuccessRate,
		ReviewPassRate:                 result.ReviewPassRate,
		PendingReviewBacklog:           result.PendingReviewBacklog,
		PostProcessingBacklog:          result.PostProcessingBacklog,
		PendingProcessingBacklog:       result.PendingProcessingBacklog,
		CollectorCount:                 result.CollectorCount,
		FullProcessPassRate:            result.FullProcessPassRate,
		AvgEpisodeCountPerPerson:       result.AvgEpisodeCountPerPerson,
		AvgCollectionDurationPerPerson: result.AvgCollectionDurationPerPerson,
	}

	return out, nil
}
// tenant-isolated
func (s *sCollect) UpdateStatInfoCache(ctx context.Context, in model.GetStatInfoInput) error {
	cacheData, err := GetStatInfoInDB(ctx, in)
	if err != nil {
		return err
	}
	// 生成缓存key
	cacheKey := "stat_info"
	if in.TaskID != 0 {
		cacheKey = fmt.Sprintf("%s:task_%d", cacheKey, in.TaskID)
	}
	if in.JobID != 0 {
		cacheKey = fmt.Sprintf("%s:job_%d", cacheKey, in.JobID)
	}

	// 更新Redis缓存，不设置过期时间，由外部worker定时更新
	_, err = g.Redis().Set(ctx, cacheKey, cacheData)
	if err != nil {
		return gerror.WrapCode(codes.CodeGetStatInfoFailed, err)
	}

	return nil
}

// tenant-isolated
func (s *sCollect) GetStatTaskCollect(ctx context.Context, in model.GetStatTaskInput) (*model.GetStatTaskCollectOutput, error) {
	type Task struct {
		ID   uint
		Name string
	}
	type Episode struct {
		TaskID uint `json:"task_id"`
		Count  int  `json:"count"`
	}
	var (
		err    error
		epiAgg []Episode
		output model.GetStatTaskCollectOutput
	)
	episodeCond := dao.Episode.Ctx(ctx).Handler(dao.WithTenantID()).Fields("task_id, COUNT(1) AS count")

	if in.TaskID == 0 {
		start, end := utility.GenerateDateRangeStrings(30)
		if in.CreatedAt != "" {
			start, end, err = utility.ParseDateStartEnd(consts.TimeFormatDateOnly, in.CreatedAt)
			if err != nil {
				g.Log().Errorf(ctx, "%v", err)
			}
		}
		// 获取全部运营中的任务
		var (
			tasks   []Task
			taskIDs []uint
		)
		taskNameMap := make(map[uint]string)
		err := dao.Task.Ctx(ctx).Handler(dao.WithTenantID()).Where(do.Task{Status: consts.TaskStatusOperational}).
			WhereGTE(dao.Episode.Columns().CreatedAt, utility.StartTimeFill(start)).
			WhereLTE(dao.Episode.Columns().CreatedAt, utility.EndTimeFill(end)).
			Scan(&tasks)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatTaskCollectFailed, err)
		}
		for _, task := range tasks {
			taskIDs = append(taskIDs, task.ID)
			taskNameMap[task.ID] = task.Name
		}
		err = episodeCond.
			WhereIn(dao.Episode.Columns().TaskId, taskIDs).
			Group(dao.Episode.Columns().TaskId).Scan(&epiAgg)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatTaskCollectFailed, err)
		}
		requiredCountMap, err := getRequiredCollectCount(ctx, taskIDs)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatTaskCollectFailed, err)
		}
		for _, item := range epiAgg {
			output.List = append(output.List, model.TaskCollectAggItem{
				TaskID:         item.TaskID,
				TaskName:       taskNameMap[item.TaskID],
				CollectedCount: item.Count,
				RequiredCount:  requiredCountMap[item.TaskID],
			})
			output.CollectedCount += item.Count
			output.RequiredCount += requiredCountMap[item.TaskID]
		}
		return &output, nil
	}
	record, err := dao.Task.Ctx(ctx).Handler(dao.WithTenantID()).Where(do.Task{Id: in.TaskID}).One()
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatTaskCollectFailed, err)
	}
	var one Task
	err = record.Struct(&one)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatTaskCollectFailed, err)
	}
	err = episodeCond.Where(dao.Episode.Columns().TaskId, one.ID).
		Group(dao.Episode.Columns().TaskId).Scan(&epiAgg)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatTaskCollectFailed, err)
	}
	requiredCountMap, err := getRequiredCollectCount(ctx, []uint{one.ID})
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatTaskCollectFailed, err)
	}
	for _, item := range epiAgg {
		output.List = append(output.List, model.TaskCollectAggItem{
			TaskID:         item.TaskID,
			TaskName:       one.Name,
			CollectedCount: item.Count,
			RequiredCount:  requiredCountMap[item.TaskID],
		})
	}
	return &output, nil
}

// tenant-isolated
func getRequiredCollectCount(ctx context.Context, taskIDs []uint) (map[uint]int, error) {
	type JobTaskRequiredCount struct {
		TaskID         uint `json:"task_id"`
		RequiredRepeat int  `json:"required_repeat"`
		RequiredMember int  `json:"required_member"`
	}
	var jobCount []JobTaskRequiredCount
	err := dao.Job.Ctx(ctx).Handler(dao.WithTenantID()).WhereIn(dao.Job.Columns().TaskId, taskIDs).Scan(&jobCount)
	if err != nil {
		return nil, err
	}
	output := make(map[uint]int)
	for _, item := range jobCount {
		output[item.TaskID] += item.RequiredRepeat * item.RequiredMember
	}
	return output, nil
}

// tenant-isolated
func (s *sCollect) GetStatTaskCheck(ctx context.Context, in model.GetStatTaskInput) (*model.GetStatTaskCheckOutput, error) {
	var err error
	type QueryTaskID struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	}
	var (
		list               []model.TaskCheckAggItem
		checkedCount       int
		requiredCheckCount int
	)

	// query task id
	var queryTaskIDs []QueryTaskID
	var taskIDs []uint
	taskMap := make(map[uint]string)
	cond := dao.Episode.Ctx(ctx).Handler(dao.WithTenantID()).
		Fields(dao.Episode.Columns().TaskId, dao.Episode.Columns().Status, "count(0) as count")

	if in.TaskID == 0 {
		// query time range
		start, end := utility.GenerateDateRangeStrings(30)
		if in.CreatedAt != "" {
			start, end, err = utility.ParseDateStartEnd(consts.TimeFormatDateOnly, in.CreatedAt)
			if err != nil {
				g.Log().Errorf(ctx, "%v", err)
			}
		}
		err := dao.Task.Ctx(ctx).Handler(dao.WithTenantID()).Fields(
			dao.Task.Columns().Id,
			dao.Task.Columns().Name,
		).
			Where(do.Task{Status: consts.TaskStatusOperational}).
			WhereGTE(dao.Episode.Columns().CreatedAt, utility.StartTimeFill(start)).
			WhereLTE(dao.Episode.Columns().CreatedAt, utility.EndTimeFill(end)).
			Scan(&queryTaskIDs)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatTaskCheckFailed, err)
		}
		for _, item := range queryTaskIDs {
			taskMap[item.ID] = item.Name
			taskIDs = append(taskIDs, item.ID)
		}
	} else {
		taskIDs = append(taskIDs, in.TaskID)
	}
	if len(taskIDs) < 1 {
		return &model.GetStatTaskCheckOutput{
			List: []model.TaskCheckAggItem{},
		}, nil
	}

	type QueryEpisode struct {
		TaskID uint
		Status uint
		Count  int
	}
	var queryEpisodes []QueryEpisode
	err = cond.WhereIn(dao.Episode.Columns().TaskId, taskIDs).
		WhereIn(dao.Episode.Columns().Status, []uint{
			consts.EpisodeStatusCheckPass,
			consts.EpisodeStatusCheckNotPass,
			consts.EpisodeStatusSystemCheckPass,
			consts.EpisodeStatusNotCheck,
		}).Group(dao.Episode.Columns().TaskId, dao.Episode.Columns().Status).Scan(&queryEpisodes)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatTaskCheckFailed, err)
	}
	taskCheckAggMap := make(map[uint]model.EpisodeStatusCount)
	for idx := range queryEpisodes {
		count := taskCheckAggMap[queryEpisodes[idx].TaskID]
		switch queryEpisodes[idx].Status {
		case consts.EpisodeStatusCheckPass, consts.EpisodeStatusSystemCheckPass:
			count.CheckPass += queryEpisodes[idx].Count
			checkedCount += queryEpisodes[idx].Count
			requiredCheckCount += queryEpisodes[idx].Count
		case consts.EpisodeStatusCheckNotPass:
			count.CheckNotPass += queryEpisodes[idx].Count
			checkedCount += queryEpisodes[idx].Count
			requiredCheckCount += queryEpisodes[idx].Count
		case consts.EpisodeStatusNotCheck:
			count.NotCheck += queryEpisodes[idx].Count
			requiredCheckCount += queryEpisodes[idx].Count
		}
		taskCheckAggMap[queryEpisodes[idx].TaskID] = count
	}
	for taskID, episodeStatusCount := range taskCheckAggMap {
		list = append(list, model.TaskCheckAggItem{
			TaskID:      taskID,
			TaskName:    taskMap[taskID],
			StatusCount: episodeStatusCount,
		})
	}
	return &model.GetStatTaskCheckOutput{
		List:               list,
		CheckedCount:       checkedCount,
		RequiredCheckCount: requiredCheckCount,
	}, nil

}

// tenant-isolated
func (s *sCollect) GetStatDate(ctx context.Context, in model.GetStatDateInput) (*model.GetStatDateOutput, error) {
	switch in.Type {
	case consts.StatCollect:
		return getStatCollectDate(ctx, in)
	case consts.StatCheck:
		return getStatCheckDate(ctx, in)
	case consts.StatSize:
		return getStatSizeDate(ctx, in)
	default:
		return getStatCollectDate(ctx, in)
	}

}

// tenant-isolated
func (s *sCollect) GetStatDateCSV(ctx context.Context, in model.GetStatDateInput) (*model.GetStatDateCSVOutput, error) {

	switch in.Type {
	case consts.StatCollect:
		return getStatCollectDateCSV(ctx, in)
	case consts.StatCheck:
		return getStatCheckDateCSV(ctx, in)
	default:
		return getStatCollectDateCSV(ctx, in)
	}
}

// tenant-isolated
func getStatCollectDateCSV(ctx context.Context, in model.GetStatDateInput) (*model.GetStatDateCSVOutput, error) {
	var (
		err        error
		useHourAgg bool
	)
	var res [][]string
	var out model.GetStatDateCSVOutput
	m := dao.Episode.Ctx(ctx)
	cond := m.Builder()
	var filename string
	if in.TaskID > 0 {
		type TaskName struct {
			Name string `json:"name"`
		}
		var taskName TaskName
		err := dao.Task.Ctx(ctx).Handler(dao.WithTenantID()).Where("id", in.TaskID).Scan(&taskName)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		filename = fmt.Sprintf("%s_collect_detail_%d.csv", taskName.Name, time.Now().Unix())
		cond = cond.Where("e.task_id", in.TaskID)
	} else {
		filename = fmt.Sprintf("collect_detail_%d.csv", time.Now().Unix())
	}
	out.Filename = filename
	if in.JobID > 0 {
		cond = cond.Where("e.job_id", in.JobID)
	}

	start, end := utility.GenerateDateRangeStrings(7)
	if in.CreatedAt != "" {
		start, end, err = utility.ParseDateStartEnd(consts.TimeFormatDateOnly, in.CreatedAt)
		if err != nil {
			g.Log().Errorf(ctx, "%v", err)
		}
		useHourAgg = gstr.Equal(start, end)
	}
	createdDateField := "DATE(e.created_at)"
	if useHourAgg {
		createdDateField = "DATE_FORMAT(e.created_at, '%H')"
	}
	header, headerIndex := utility.GenerateDateRange(start, end)

	res = append(res, header)
	cond = cond.WhereGTE("e.created_at", utility.StartTimeFill(start))
	cond = cond.WhereLTE("e.created_at", utility.EndTimeFill(end))

	if len(in.Status) > 0 {
		cond = cond.WhereIn("e.status", in.Status)
	} else {
		cond = cond.WhereIn("e.status", consts.GetEpisodeStatus(consts.EpisodeStatusTypeCollected))
	}

	type Res struct {
		Count       int    `json:"count"`
		CreatedDate string `json:"created_date"`
		UserID      string `json:"user_id"`
		DisplayName string `json:"display_name"`
	}
	type TaskUser struct {
		UserID      string `json:"user_id"`
		DisplayName string `json:"display_name"`
	}
	var queryRes []Res
	var userIDs []string
	var taskUsers []TaskUser
	taskUserMap := make(map[string]string)

	if in.UserID == "" {
		err := dao.Episode.Ctx(ctx).As("e").LeftJoin(dao.Assignment.Table(), "a", "e.assignment_id=a.id").
			Fields("a.user_id, " + createdDateField + " AS created_date, COUNT(1) AS count").
			Handler(dao.WithTenantIDForAlias("e")).
			Where(cond).Group("a.user_id").
			Group(createdDateField).OrderAsc(createdDateField).Scan(&queryRes)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}

	} else {
		// 通过采集员userID查询
		cond = cond.Where("a.user_id", in.UserID)
		err = dao.Episode.Ctx(ctx).As("e").LeftJoin(dao.Assignment.Table(), "a", "e.assignment_id=a.id").
			Fields("a.user_id, " + createdDateField + " AS created_date, COUNT(1) AS count").
			Handler(dao.WithTenantIDForAlias("e")).
			Where(cond).Group("a.user_id").
			Group(createdDateField).OrderAsc(createdDateField).Scan(&queryRes)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
	}
	for i := range queryRes {
		userIDs = append(userIDs, queryRes[i].UserID)
	}
	err = dao.TaskUser.Ctx(ctx).Handler(dao.WithTenantID()).Fields("distinct display_name", "user_id").WhereIn("user_id", userIDs).Scan(&taskUsers)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	for i := range taskUsers {
		taskUserMap[taskUsers[i].UserID] = taskUsers[i].DisplayName
	}
	for i := range queryRes {
		queryRes[i].DisplayName = taskUserMap[queryRes[i].UserID]
	}

	queryResMap := make(map[string][]Res)
	for i := range queryRes {
		if value, ok := queryResMap[queryRes[i].DisplayName]; ok {
			value = append(value, queryRes[i])
			queryResMap[queryRes[i].DisplayName] = value
		} else {
			queryResMap[queryRes[i].DisplayName] = []Res{queryRes[i]}
		}
	}
	for displayName, detail := range queryResMap {
		// 初始化csv data
		data := make([]string, len(header))
		for i := range data {
			data[i] = "0"
		}
		data[0] = displayName
		for i := range detail {
			data[headerIndex[detail[i].CreatedDate]] = strconv.Itoa(detail[i].Count)
		}
		res = append(res, data)
	}
	out.Data = res
	return &out, nil
}

// tenant-isolated
func getStatCollectDate(ctx context.Context, in model.GetStatDateInput) (*model.GetStatDateOutput, error) {
	var (
		err        error
		list       []model.DateTaskAggItem
		useHourAgg bool
	)

	m := dao.Episode.Ctx(ctx)
	cond := m.Builder()
	if in.TaskID > 0 {
		cond = cond.Where("e.task_id", in.TaskID)
	}
	if in.JobID > 0 {
		cond = cond.Where("e.job_id", in.JobID)
	}

	start, end := utility.GenerateDateRangeStrings(7)
	if in.CreatedAt != "" {
		start, end, err = utility.ParseDateStartEnd(consts.TimeFormatDateOnly, in.CreatedAt)
		if err != nil {
			g.Log().Errorf(ctx, "%v", err)
		}
		useHourAgg = gstr.Equal(start, end)
	}
	createdDateField := "DATE(e.created_at)"
	if useHourAgg {
		createdDateField = "DATE_FORMAT(e.created_at, '%Y-%m-%d %H')"
	}
	cond = cond.WhereGTE("e.created_at", utility.StartTimeFill(start))
	cond = cond.WhereLTE("e.created_at", utility.EndTimeFill(end))

	if len(in.Status) > 0 {
		cond = cond.WhereIn("e.status", in.Status)
	} else {
		cond = cond.WhereIn("e.status", consts.GetEpisodeStatus(consts.EpisodeStatusTypeCollected))
	}
	if in.UserID == "" {
		result, err := dao.Episode.Ctx(ctx).As("e").
			Fields(createdDateField+" AS created_date, task_id, COUNT(1) AS count").
			Where(cond).
			Handler(dao.WithTenantIDForAlias("e")).
			Group(createdDateField, "task_id").OrderAsc(createdDateField).All()
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		err = result.Structs(&list)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
	} else {
		// 通过采集员userID查询
		cond = cond.Where("a.user_id", in.UserID)
		result, err := dao.Episode.Ctx(ctx).As("e").LeftJoin(dao.Assignment.Table(), "a", "e.assignment_id=a.id").
			Fields(createdDateField+" AS created_date, e.task_id AS task_id, COUNT(1) AS count").
			Where(cond).
			Handler(dao.WithTenantIDForAlias("e")).
			Group(createdDateField, "e.task_id").OrderAsc(createdDateField).All()
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		err = result.Structs(&list)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
	}
	for i := range list {
		if useHourAgg {
			list[i].CreatedDate = hourFormat(list[i].CreatedDate, start)
		}
	}
	output, err := parseDateTaskAggToDateAgg(ctx, list)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	return &model.GetStatDateOutput{
		List: output,
	}, nil
}

// tenant-isolated
func parseDateTaskAggToDateAgg(ctx context.Context, list []model.DateTaskAggItem) ([]model.DateAggItem, error) {
	var (
		taskIDs []uint
		tasks   []entity.Task
		output  []model.DateAggItem
	)
	taskNameM := make(map[uint]string)
	for _, item := range list {
		taskNameM[item.TaskID] = ""
	}
	for id := range taskNameM {
		taskIDs = append(taskIDs, id)
	}
	err := dao.Task.Ctx(ctx).Handler(dao.WithTenantID()).WhereIn("id", taskIDs).Scan(&tasks)
	if err != nil {
		return nil, err
	}
	for _, task := range tasks {
		taskNameM[task.Id] = task.Name
	}
	for i, item := range list {
		list[i].TaskName = taskNameM[item.TaskID]
	}
	dateTasksM := make(map[string][]model.DateTaskAggItem)
	for _, item := range list {
		if dateTasksM[item.CreatedDate] == nil {
			output = append(output, model.DateAggItem{
				CreatedDate: item.CreatedDate,
			})
		}
		dateTasksM[item.CreatedDate] = append(dateTasksM[item.CreatedDate], item)
	}
	for i, item := range output {
		cDetail := make(map[string]int64)
		pDetail := make(map[string]int64)
		for _, taskAgg := range dateTasksM[item.CreatedDate] {
			if taskAgg.TaskName == "" {
				continue
			}
			cDetail[gstr.Trim(taskAgg.TaskName)] = taskAgg.Count
			pDetail[gstr.Trim(taskAgg.TaskName)] = taskAgg.Passed
			output[i].Count += taskAgg.Count
			output[i].Passed += taskAgg.Passed
		}
		output[i].CountDetail = cDetail
		output[i].PassedDetail = pDetail
	}
	return output, nil
}

func hourFormat(key, trim string) string {
	return gstr.Trim(gstr.TrimStr(key, trim)) + ":00"
}

// tenant-isolated
func getStatCheckDate(ctx context.Context, in model.GetStatDateInput) (*model.GetStatDateOutput, error) {
	var (
		err        error
		list       []model.DateTaskAggItem
		passed     []model.DateTaskAggItem
		useHourAgg bool
	)
	reviewDAO := dao.Review.Ctx(ctx).Handler(dao.WithTenantID())
	if in.UserID != "" {
		reviewDAO = reviewDAO.Where("user_id", in.UserID)
	}
	if in.TaskID > 0 {
		reviewDAO = reviewDAO.Where("task_id", in.TaskID)
	}
	if in.JobID > 0 {
		reviewDAO = reviewDAO.Where("job_id", in.JobID)
	}
	start, end := utility.GenerateDateRangeStrings(7)
	if in.CreatedAt != "" {
		start, end, err = utility.ParseDateStartEnd(consts.TimeFormatDateOnly, in.CreatedAt)
		if err != nil {
			g.Log().Errorf(ctx, "%v", err)
		}
		useHourAgg = gstr.Equal(start, end)
	}
	createdDateField := "DATE(created_at)"
	if useHourAgg {
		createdDateField = "DATE_FORMAT(created_at, '%Y-%m-%d %H')"
	}
	reviewDAO = reviewDAO.WhereGTE("created_at", utility.StartTimeFill(start))
	reviewDAO = reviewDAO.WhereLTE("created_at", utility.EndTimeFill(end))
	// 全部审核量
	res, err := reviewDAO.Fields(createdDateField+" AS created_date, task_id, COUNT(1) AS count").
		Group(createdDateField, "task_id").
		OrderAsc(createdDateField).All()
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	err = res.Structs(&list)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	// 审核通过量
	res, err = reviewDAO.
		Where(dao.Review.Columns().Status, consts.EpisodeStatusCheckPass).
		Fields(createdDateField+" AS created_date, task_id, COUNT(1) AS passed").
		Group(createdDateField, "task_id").
		OrderAsc(createdDateField).All()
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	err = res.Structs(&passed)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}

	passedAgg := make(map[string]int64)
	for _, item := range passed {
		passedAgg[fmt.Sprintf("%s-%d", item.CreatedDate, item.TaskID)] = item.Passed
	}

	for i, item := range list {
		list[i].Passed = passedAgg[fmt.Sprintf("%s-%d", item.CreatedDate, item.TaskID)]
		if useHourAgg {
			list[i].CreatedDate = hourFormat(item.CreatedDate, start)
		}
	}
	output, err := parseDateTaskAggToDateAgg(ctx, list)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	return &model.GetStatDateOutput{
		List: output,
	}, nil
}

// tenant-isolated
func getStatCheckDateCSV(ctx context.Context, in model.GetStatDateInput) (*model.GetStatDateCSVOutput, error) {
	var (
		err        error
		useHourAgg bool
	)
	var res [][]string
	var out model.GetStatDateCSVOutput
	var filename string

	reviewDAO := dao.Review.Ctx(ctx).Handler(dao.WithTenantID())
	if in.UserID != "" {
		reviewDAO = reviewDAO.Where("user_id", in.UserID)
	}
	if in.TaskID > 0 {
		reviewDAO = reviewDAO.Where("task_id", in.TaskID)
		type TaskName struct {
			Name string `json:"name"`
		}
		var taskName TaskName
		err := dao.Task.Ctx(ctx).Handler(dao.WithTenantID()).Where("id", in.TaskID).Scan(&taskName)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		filename = fmt.Sprintf("%s_check_detail_%d.csv", taskName.Name, time.Now().Unix())
	} else {
		filename = fmt.Sprintf("check_detail_%d.csv", time.Now().Unix())
	}
	out.Filename = filename

	if in.JobID > 0 {
		reviewDAO = reviewDAO.Where("job_id", in.JobID)
	}
	start, end := utility.GenerateDateRangeStrings(7)
	if in.CreatedAt != "" {
		start, end, err = utility.ParseDateStartEnd(consts.TimeFormatDateOnly, in.CreatedAt)
		if err != nil {
			g.Log().Errorf(ctx, "%v", err)
		}
		useHourAgg = gstr.Equal(start, end)
	}
	createdDateField := "DATE(created_at)"
	if useHourAgg {
		createdDateField = "DATE_FORMAT(created_at, '%H')"
	}
	header, headerIndex := utility.GenerateDateRange(start, end)
	res = append(res, header)

	type Res struct {
		Count       int    `json:"count"`
		CreatedDate string `json:"created_date"`
		UserID      string `json:"user_id"`
		DisplayName string `json:"display_name"`
		Passed      int    `json:"passed"`
	}
	type TaskUser struct {
		UserID      string `json:"user_id"`
		DisplayName string `json:"display_name"`
	}
	var userIDs []string
	var taskUsers []TaskUser
	taskUserMap := make(map[string]string)
	queryResMap := make(map[string]map[string]Res)

	reviewDAO = reviewDAO.WhereGTE("created_at", utility.StartTimeFill(start))
	reviewDAO = reviewDAO.WhereLTE("created_at", utility.EndTimeFill(end))

	var totalCheck []Res
	var checkedPass []Res

	// 全部审核量
	err = reviewDAO.Fields("user_id," + createdDateField + " AS created_date, COUNT(1) AS count").
		Group("user_id").Group(createdDateField).
		OrderAsc(createdDateField).Scan(&totalCheck)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}

	// 审核通过量
	err = reviewDAO.
		Where(dao.Review.Columns().Status, consts.EpisodeStatusCheckPass).
		Fields("user_id," + createdDateField + " AS created_date, COUNT(1) AS passed").
		Group("user_id").
		Group(createdDateField).
		OrderAsc(createdDateField).Scan(&checkedPass)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}

	for i := range totalCheck {
		userIDs = append(userIDs, totalCheck[i].UserID)
	}
	for i := range checkedPass {
		userIDs = append(userIDs, checkedPass[i].UserID)
	}
	err = dao.TaskUser.Ctx(ctx).Handler(dao.WithTenantID()).Fields("distinct display_name", "user_id").WhereIn("user_id", userIDs).Scan(&taskUsers)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	for i := range taskUsers {
		taskUserMap[taskUsers[i].UserID] = taskUsers[i].DisplayName
	}
	for i := range totalCheck {
		totalCheck[i].DisplayName = taskUserMap[totalCheck[i].UserID]
	}
	for i := range checkedPass {
		checkedPass[i].DisplayName = taskUserMap[checkedPass[i].UserID]
	}
	for i := range totalCheck {
		if value, ok := queryResMap[totalCheck[i].DisplayName]; ok {
			value[totalCheck[i].CreatedDate] = totalCheck[i]
			queryResMap[totalCheck[i].DisplayName] = value
		} else {
			childMap := make(map[string]Res)
			childMap[totalCheck[i].CreatedDate] = totalCheck[i]
			queryResMap[totalCheck[i].DisplayName] = childMap
		}
	}
	for i := range checkedPass {
		if value, ok := queryResMap[checkedPass[i].DisplayName]; ok {
			childRes := value[checkedPass[i].CreatedDate]
			childRes.Passed = checkedPass[i].Passed
			queryResMap[checkedPass[i].DisplayName][checkedPass[i].CreatedDate] = childRes
		}
	}
	for displayName, detail := range queryResMap {
		// 初始化csv data
		data := make([]string, len(header))
		for i := range data {
			data[i] = "0"
		}
		data[0] = displayName
		for date, childRes := range detail {
			data[headerIndex[date]] = strconv.Itoa(childRes.Passed) + "//" + strconv.Itoa(childRes.Count)
		}
		res = append(res, data)
	}
	out.Data = res

	return &out, err

}

// tenant-isolated
func getStatSizeDate(ctx context.Context, in model.GetStatDateInput) (*model.GetStatDateOutput, error) {
	var (
		err        error
		list       []model.DateAggItem
		useHourAgg bool
	)
	m := dao.Episode.Ctx(ctx)
	cond := m.Builder()
	if in.TaskID > 0 {
		cond = cond.Where("task_id", in.TaskID)
	}
	if in.JobID > 0 {
		cond = cond.Where("job_id", in.JobID)
	}
	start, end := utility.GenerateDateRangeStrings(7)
	if in.CreatedAt != "" {
		start, end, err = utility.ParseDateStartEnd(consts.TimeFormatDateOnly, in.CreatedAt)
		if err != nil {
			g.Log().Errorf(ctx, "%v", err)
		}
		useHourAgg = gstr.Equal(start, end)
	}
	createdDateField := "DATE(created_at)"
	if useHourAgg {
		createdDateField = "DATE_FORMAT(created_at, '%Y-%m-%d %H')"
	}
	cond = cond.WhereGTE("created_at", utility.StartTimeFill(start))
	cond = cond.WhereLTE("created_at", utility.EndTimeFill(end))
	if len(in.Status) > 0 {
		cond = cond.WhereIn("status", in.Status)
	} else {
		cond = cond.WhereIn("status", []uint{
			consts.EpisodeStatusChecking,
			consts.EpisodeStatusCheckNotPass,
			consts.EpisodeStatusCheckPass,
			consts.EpisodeStatusNotCheck,
			consts.EpisodeStatusSystemCheckPass,
		})
	}
	result, err := dao.Episode.Ctx(ctx).Handler(dao.WithTenantID()).
		Fields(createdDateField + " AS created_date, SUM(size) AS count").
		Where(cond).
		Group(createdDateField).OrderAsc(createdDateField).All()
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	err = result.Structs(&list)

	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	for i := range list {
		if useHourAgg {
			list[i].CreatedDate = hourFormat(list[i].CreatedDate, start)
		}
	}
	return &model.GetStatDateOutput{
		List: list,
	}, nil
}

// tenant-isolated
func (s *sCollect) GetStatUser(ctx context.Context, in model.GetStatUserInput) (*model.GetStatUserOutput, error) {
	var err error
	m := dao.Episode.Ctx(ctx)
	cond := m.Builder()

	reviewM := dao.Review.Ctx(ctx)
	reviewCond := reviewM.Builder()

	if in.TaskID > 0 {
		cond = cond.Where("e.task_id", in.TaskID)
		reviewCond = reviewCond.Where("r.task_id", in.TaskID)
	}
	if in.JobID > 0 {
		cond = cond.Where("e.job_id", in.JobID)
		reviewCond = reviewCond.Where("r.job_id", in.JobID)
	}
	start, end := utility.GenerateDateRangeStrings(7)
	if in.CreatedAt != "" {
		start, end, err = utility.ParseDateStartEnd(consts.TimeFormatDateOnly, in.CreatedAt)
		if err != nil {
			g.Log().Errorf(ctx, "%v", err)
		}
	}
	cond = cond.WhereGTE("e.created_at", utility.StartTimeFill(start))
	cond = cond.WhereLTE("e.created_at", utility.EndTimeFill(end))

	reviewCond = reviewCond.WhereGTE("r.created_at", utility.StartTimeFill(start))
	reviewCond = reviewCond.WhereLTE("r.created_at", utility.EndTimeFill(end))

	passCountCond := cond.WhereIn("e.status", consts.GetEpisodeStatus(consts.EpisodeStatusTypeCheckPass))
	checkCountCond := cond.WhereIn("e.status", consts.GetEpisodeStatus(consts.EpisodeStatusTypeChecked))
	cond = cond.WhereIn("e.status", consts.GetEpisodeStatus(consts.EpisodeStatusTypeCollected))
	var list []model.UserAggItem

	switch in.Type {
	case consts.StatCollect:
		result, err := dao.Episode.Ctx(ctx).As("e").LeftJoin(dao.Assignment.Table(), "a", "e.assignment_id=a.id").
			Fields("a.username AS username, COUNT(1) AS count").
			Where(cond).
			Handler(dao.WithTenantIDForAlias("e")).
			Group("a.username").OrderDesc("count").All()
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatUserFailed, err)
		}
		err = result.Structs(&list)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatUserFailed, err)
		}

		type CheckCount struct {
			Username string
			Count    int64
		}
		// 审核总量
		checkCountMap := make(map[string]int64)
		checkPassCountMap := make(map[string]int64)
		var checkCounts []CheckCount
		var checkPassCounts []CheckCount
		checkCountResult, err := dao.Episode.Ctx(ctx).As("e").LeftJoin(dao.Assignment.Table(), "a", "e.assignment_id=a.id").
			Fields("a.username AS username, COUNT(1) AS count").
			Handler(dao.WithTenantIDForAlias("e")).
			Where(checkCountCond).Group("a.username").OrderDesc("count").All()
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatUserFailed, err)
		}
		err = checkCountResult.Structs(&checkCounts)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatUserFailed, err)
		}
		for _, item := range checkCounts {
			checkCountMap[item.Username] = item.Count
		}

		// 审核通过量
		passResult, err := dao.Episode.Ctx(ctx).As("e").LeftJoin(dao.Assignment.Table(), "a", "e.assignment_id=a.id").
			Fields("a.username AS username, COUNT(1) AS count").
			Handler(dao.WithTenantIDForAlias("e")).
			Where(passCountCond).Group("a.username").OrderDesc("count").All()
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatUserFailed, err)
		}
		err = passResult.Structs(&checkPassCounts)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatUserFailed, err)
		}
		for _, item := range checkPassCounts {
			checkPassCountMap[item.Username] = item.Count
		}

		for index, item := range list {
			item.CheckCount = checkCountMap[item.Username]
			item.CheckPassCount = checkPassCountMap[item.Username]
			list[index] = item
		}
	case consts.StatCheck:
		result, err := dao.Review.Ctx(ctx).As("r").
			Fields("r.username AS username, COUNT(1) AS count").
			Where(reviewCond).
			Handler(dao.WithTenantIDForAlias("r")).
			Group("r.username").OrderDesc("count").All()
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatUserFailed, err)
		}
		err = result.Structs(&list)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatUserFailed, err)
		}
	}

	var wg sync.WaitGroup
	for i, item := range list {
		wg.Add(1)
		go processItem(ctx, i, item, list, &wg)
	}
	wg.Wait()
	return &model.GetStatUserOutput{
		List: list,
	}, nil
}

// tenant-isolated
func processItem(ctx context.Context, i int, item model.UserAggItem, list []model.UserAggItem, wg *sync.WaitGroup) {
	defer wg.Done()
	usr, err := iam.Client(ctx).GetUserByName(item.Username)
	if err != nil || usr == nil {
		fmt.Printf("Not found username %s: %v\n", item.Username, err)
		return
	}
	list[i].UserID = usr.Id
	list[i].DisplayName = usr.DisplayName
}

// tenant-isolated
func (s *sCollect) GetStatReason(ctx context.Context, in model.GetStatReasonInput) (*model.GetStatReasonOutput, error) {
	reviewDAO := dao.Review.Ctx(ctx)
	if in.TaskID > 0 {
		reviewDAO = reviewDAO.Where("task_id", in.TaskID)
	} else {
		taskIDs, err := GetTaskIDsByTimeRange(ctx, 7)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		if len(taskIDs) >= 1 {
			reviewDAO = reviewDAO.WhereIn(dao.Review.Columns().TaskId, taskIDs)
		}
	}
	if in.JobID > 0 {
		reviewDAO = reviewDAO.Where("job_id", in.JobID)
	}

	if in.UserID != "" {
		var assignments []entity.Assignment
		var assignmentIDs []uint
		err := dao.Assignment.Ctx(ctx).Handler(dao.WithTenantID()).Where(do.Assignment{UserId: in.UserID, TaskId: in.TaskID}).Scan(&assignments)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		if len(assignments) < 1 {
			return &model.GetStatReasonOutput{Reasons: []model.StatReasonItem{}}, nil
		}
		for _, item := range assignments {
			assignmentIDs = append(assignmentIDs, item.Id)
		}

		var episodes []entity.Episode
		var episodeIDs []uint64
		err = dao.Episode.Ctx(ctx).Handler(dao.WithTenantID()).Where(do.Episode{TaskId: in.TaskID}).WhereIn(dao.Episode.Columns().AssignmentId, assignmentIDs).Scan(&episodes)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		if len(episodes) < 1 {
			return &model.GetStatReasonOutput{
				Reasons: []model.StatReasonItem{},
			}, nil
		}
		for _, item := range episodes {
			episodeIDs = append(episodeIDs, item.Id)
		}
		reviewDAO = reviewDAO.WhereIn(dao.Review.Columns().EpisodeId, episodeIDs)
	}
	res, err := reviewDAO.
		Handler(dao.WithTenantID()).
		WhereNot(dao.Review.Columns().Status, consts.EpisodeStatusCheckPass).
		Fields("comment, COUNT(1) AS count").
		Group("comment").All()
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	var (
		reasons []model.StatReasonItem
	)
	err = res.Structs(&reasons)

	// 计算总计
	var total int64
	for _, reason := range reasons {
		total += reason.Count
	}
	// 计算每个项的 Rate
	for i := range reasons {
		if total > 0 {
			reasons[i].Rate = math.Round(float64(reasons[i].Count)*100/float64(total)*100) / 100 // 保留两位小数
		} else {
			reasons[i].Rate = 0 // 如果总计为0，Rate设为0
		}
	}

	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	return &model.GetStatReasonOutput{Reasons: reasons}, nil
}

// tenant-isolated
func (s *sCollect) GetStatReasonCSV(ctx context.Context, in model.GetStatReasonInput) (*model.GetStatDateCSVOutput, error) {
	var out model.GetStatDateCSVOutput
	var data [][]string
	data = append(data, []string{"job_id", "episode_id", "采集员", "审核员", "错误原因", "path"})
	reviewDAO := dao.Review.Ctx(ctx)
	var taskIDs []uint
	if in.TaskID > 0 {
		taskIDs = append(taskIDs, in.TaskID)
	} else {
		var innerTaskIDs []uint
		innerTaskIDs, err := GetTaskIDsByTimeRange(ctx, 7)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		taskIDs = innerTaskIDs
	}
	if len(taskIDs) >= 1 {
		reviewDAO = reviewDAO.WhereIn("r.task_id", taskIDs)
	}

	if in.JobID > 0 {
		reviewDAO = reviewDAO.Where("r.job_id", in.JobID)
	}

	if in.UserID != "" {
		var assignments []entity.Assignment
		var assignmentIDs []uint
		err := dao.Assignment.Ctx(ctx).
			Handler(dao.WithTenantID()).
			Where(do.Assignment{UserId: in.UserID}).
			WhereIn("task_id", taskIDs).
			Scan(&assignments)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		if len(assignments) < 1 {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, gerror.New("查询为空"))
		}
		for _, item := range assignments {
			assignmentIDs = append(assignmentIDs, item.Id)
		}

		var episodes []entity.Episode
		var episodeIDs []uint64
		err = dao.Episode.Ctx(ctx).WhereIn("task_id", taskIDs).WhereIn(dao.Episode.Columns().AssignmentId, assignmentIDs).Scan(&episodes)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}
		if len(episodes) < 1 {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, gerror.New("查询为空"))
		}
		for _, item := range episodes {
			episodeIDs = append(episodeIDs, item.Id)
		}
		reviewDAO = reviewDAO.WhereIn(dao.Review.Columns().EpisodeId, episodeIDs)
	}

	type QueryRes struct {
		JobID     uint   `json:"job_id"`
		EpisodeID uint   `json:"episode_id"`
		Collector string `json:"collector"`
		Checker   string `json:"checker"`
		Comment   string `json:"comment"`
		Path      string `json:"path"`
	}
	var queryRes []QueryRes
	err := reviewDAO.As("r").WhereNot("r.status", consts.EpisodeStatusCheckPass).
		LeftJoin("episode", "e", "e.id=r.episode_id").
		LeftJoin("assignment", "a", "a.id=e.assignment_id").
		Where("r.tenant_id=?", utility.GetAccessTenantId(ctx)).
		Fields("r.job_id,r.episode_id,a.user_id as collector,r.user_id as checker,r.comment,e.path").
		Scan(&queryRes)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	var userIDs []string
	for i := range queryRes {
		userIDs = append(userIDs, queryRes[i].Collector)
		userIDs = append(userIDs, queryRes[i].Checker)
	}
	userMap, err := getDisplayNameFromID(ctx, userIDs)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	singleFrameBucketStr := utility.GetSingleFrameBucket(ctx)
	for i := range queryRes {

		originBucket, _, err := utility.S3PathParse(queryRes[i].Path)
		if err != nil {
			return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
		}

		path := strings.Replace(queryRes[i].Path, originBucket, singleFrameBucketStr, 1)

		data = append(data, []string{
			strconv.Itoa(int(queryRes[i].JobID)),
			strconv.Itoa(int(queryRes[i].EpisodeID)),
			userMap[queryRes[i].Collector],
			userMap[queryRes[i].Checker],
			queryRes[i].Comment,
			path,
		})
	}
	out.Data = data
	type TaskName struct {
		Name string `json:"name"`
	}
	var taskName []TaskName
	err = dao.Task.Ctx(ctx).Handler(dao.WithTenantID()).WhereIn(dao.Task.Columns().Id, taskIDs).Fields(dao.Task.Columns().Name).Scan(&taskName)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeGetStatDateFailed, err)
	}
	if len(taskName) == 1 {
		out.Filename = fmt.Sprintf("%s_审核失败分布_%d.csv", taskName[0].Name, time.Now().Unix())
	} else {
		out.Filename = fmt.Sprintf("审核失败分布_%d.csv", time.Now().Unix())
	}

	return &out, nil
}

type ByCheckPassDuration []model.StatValidDurationItem

func (a ByCheckPassDuration) Len() int      { return len(a) }
func (a ByCheckPassDuration) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a ByCheckPassDuration) Less(i, j int) bool {
	if a[i].Role == a[j].Role {
		return a[i].CheckPassDuration > a[j].CheckPassDuration
	} else {
		return a[i].Role > a[j].Role
	}

}

// tenant-isolated
func (s *sCollect) ValidDurationCSV(ctx context.Context, timeRange string) (*model.GetStatDateCSVOutput, error) {
	var out model.GetStatDateCSVOutput
	var data [][]string
	data = append(data, []string{"采集员/审核员", "角色", "任务ID", "采集提交时长", "审核通过时长(有效时长)", "审核未通过时长", "未审核时长"})

	start, end, err := utility.ParseDateStartEnd(consts.TimeFormatDateTime, timeRange)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeDownloadValidDurationDataFailed, err)
	}

	var res []model.StatValidDurationItem
	resMap := make(map[string]model.StatValidDurationItem)
	var jobIDs []uint
	var assignmentIDs []uint
	var taskIDs []uint

	err = dao.Episode.Ctx(ctx).Handler(dao.WithTenantID()).WhereGTE(dao.Episode.Columns().CreatedAt, start).WhereLT(dao.Episode.Columns().CreatedAt, end).
		Fields("assignment_id", "task_id", "job_id", "status").
		Scan(&res)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeDownloadValidDurationDataFailed, err)
	}
	for _, item := range res {
		if !utility.HasExists(jobIDs, item.JobID) {
			jobIDs = append(jobIDs, item.JobID)
		}
		if !utility.HasExists(assignmentIDs, item.AssignmentID) {
			assignmentIDs = append(assignmentIDs, item.AssignmentID)
		}
		if !utility.HasExists(taskIDs, item.TaskID) {
			taskIDs = append(taskIDs, item.TaskID)
		}
	}

	var jobs []entity.Job
	durationMap := make(map[uint]int64)
	err = dao.Job.Ctx(ctx).Handler(dao.WithTenantID()).WhereIn("id", jobIDs).Fields(dao.Job.Columns().Id, dao.Job.Columns().Duration).Scan(&jobs)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeDownloadValidDurationDataFailed, err)
	}
	for _, item := range jobs {
		durationMap[item.Id] = item.Duration
	}

	var assignments []entity.Assignment
	assignmentMap := make(map[uint]string)
	err = dao.Assignment.Ctx(ctx).Handler(dao.WithTenantID()).WhereIn("id", assignmentIDs).Fields(dao.Assignment.Columns().Id, dao.Assignment.Columns().Username).Scan(&assignments)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeDownloadValidDurationDataFailed, err)
	}
	for _, item := range assignments {
		assignmentMap[item.Id] = item.Username
	}

	for _, item := range res {
		jobDuration := durationMap[item.JobID]
		role := consts.RoleDataCollector
		queryRes := model.StatValidDurationItem{
			AssignmentID:  item.AssignmentID,
			TaskID:        item.TaskID,
			JobID:         item.JobID,
			Status:        item.Status,
			JobDuration:   jobDuration,
			TotalDuration: jobDuration,
			UserName:      assignmentMap[item.AssignmentID],
			Role:          role,
		}
		if item.Status == consts.EpisodeStatusCheckPass || item.Status == consts.EpisodeStatusSystemCheckPass {
			queryRes.CheckPassDuration = jobDuration
		}
		if item.Status == consts.EpisodeStatusCheckNotPass {
			queryRes.CheckNotPassDuration = jobDuration
		}

		value, exists := resMap[fmt.Sprintf("%d-%s-%s", item.TaskID, queryRes.UserName, role)]
		if !exists {
			value = queryRes
		} else {
			value.TotalDuration += queryRes.TotalDuration
			value.CheckPassDuration += queryRes.CheckPassDuration
			value.CheckNotPassDuration += queryRes.CheckNotPassDuration
		}
		resMap[fmt.Sprintf("%d-%s-%s", item.TaskID, queryRes.UserName, role)] = value
	}

	// 审核员
	type QueryCheckerData struct {
		TaskID   uint   `json:"task_id"`
		JobID    uint   `json:"job_id"`
		Status   uint   `json:"status"`
		Username string `json:"username"`
	}
	var queryCheckerData []QueryCheckerData
	query := buildLabelInfoQuery(taskIDs, "episode.task_id,episode.job_id,episode.status,review.username",
		"task_id", "task_id")
	err = g.DB().GetScan(ctx, &queryCheckerData, query)
	if err != nil {
		return nil, gerror.WrapCode(codes.CodeDownloadReviewPassDataFailed, err)
	}
	for _, item := range queryCheckerData {
		jobDuration := durationMap[item.JobID]
		role := consts.RoleDataChecker
		queryRes := model.StatValidDurationItem{
			TaskID:        item.TaskID,
			JobID:         item.JobID,
			Status:        item.Status,
			JobDuration:   jobDuration,
			TotalDuration: jobDuration,
			UserName:      item.Username,
			Role:          role,
		}
		if item.Status == consts.EpisodeStatusCheckPass || item.Status == consts.EpisodeStatusSystemCheckPass {
			queryRes.CheckPassDuration = jobDuration
		}
		if item.Status == consts.EpisodeStatusCheckNotPass {
			queryRes.CheckNotPassDuration = jobDuration
		}

		value, exists := resMap[fmt.Sprintf("%d-%s-%s", item.TaskID, queryRes.UserName, role)]
		if !exists {
			value = queryRes
		} else {
			value.TotalDuration += queryRes.TotalDuration
			value.CheckPassDuration += queryRes.CheckPassDuration
			value.CheckNotPassDuration += queryRes.CheckNotPassDuration
		}
		resMap[fmt.Sprintf("%d-%s-%s", item.TaskID, queryRes.UserName, role)] = value
	}

	var outRes []model.StatValidDurationItem
	for _, value := range resMap {
		outRes = append(outRes, value)

	}

	var wg sync.WaitGroup
	var sem = make(chan struct{}, 50) // 限制并发数量为 10
	for i, item := range outRes {
		wg.Add(1)
		go func(i int, item model.StatValidDurationItem) {
			defer wg.Done()
			sem <- struct{}{}        // 获取一个信号量
			defer func() { <-sem }() // 释放信号量
			processValidDurationItem(ctx, i, item, outRes, &wg)
		}(i, item)
	}
	wg.Wait()

	sort.Sort(ByCheckPassDuration(outRes))
	for _, item := range outRes {
		notChecked := ""
		if item.Role == consts.RoleDataCollector {
			notChecked = strconv.FormatInt(item.NotCheckedDuration, 10)
		}
		data = append(data, []string{
			item.DisplayName,
			item.Role,
			strconv.Itoa(int(item.TaskID)),
			strconv.FormatInt(item.TotalDuration, 10),
			strconv.FormatInt(item.CheckPassDuration, 10),
			strconv.FormatInt(item.CheckNotPassDuration, 10),
			notChecked,
		})
	}
	out.Data = data
	out.Filename = fmt.Sprintf("%s-%s-valid-duration-export.csv", start, end)
	return &out, nil

}

// tenant-isolated
func processValidDurationItem(ctx context.Context, i int, item model.StatValidDurationItem, list []model.StatValidDurationItem, wg *sync.WaitGroup) {
	usr, err := iam.Client(ctx).GetUserByName(item.UserName)
	if err != nil || usr == nil {
		fmt.Printf("Not found username %s: %v\n", item.UserName, err)
		list[i].DisplayName = item.UserName
	} else {
		list[i].DisplayName = usr.DisplayName
	}
	list[i].NotCheckedDuration = list[i].TotalDuration - list[i].CheckPassDuration - list[i].CheckNotPassDuration
	if list[i].TotalDuration > 0 {
		list[i].TotalDuration = list[i].TotalDuration / 1000
	}
	if list[i].CheckPassDuration > 0 {
		list[i].CheckPassDuration = list[i].CheckPassDuration / 1000
	}
	if list[i].CheckNotPassDuration > 0 {
		list[i].CheckNotPassDuration = list[i].CheckNotPassDuration / 1000
	}
	if list[i].NotCheckedDuration > 0 {
		list[i].NotCheckedDuration = list[i].NotCheckedDuration / 1000
	}
}
