package dataset

import (
	"aim-data/internal/dao"
	"aim-data/internal/model"
	"aim-data/internal/model/entity"
	"context"
	"fmt"
	"os"
	"testing"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/drivers/sqlite/v2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/stretchr/testify/assert"
)

func TestMain(m *testing.M) {
	// 设置
	g.Cfg().GetAdapter().(*gcfg.AdapterFile).SetFileName("../../../manifest/config/config.yaml")

	// 运行测试
	code := m.Run()

	// 清理工作（如需要）
	// ...

	// 退出，使用测试结果作为退出码
	os.Exit(code)
}

func Test_checkNewDataGen(t *testing.T) {
	ctx := gctx.New()
	assert := assert.New(t)
	dataset := new(entity.Dataset)
	err := g.DB().Model(dao.Dataset.Table()).Where("episode_cnt > 0").Limit(1).Order("created_at").Scan(dataset)
	assert.NoError(err)

	stats, _, err := getDatasetStats(ctx, dataset.Id, dataset.ReviewStatus)
	assert.NoError(err)
	dataset.EpisodeCnt = int(stats.EpisodeCnt)
	gen, err := checkNewDataGen(ctx, dataset)
	assert.NoError(err)
	assert.Equal(gen, false)

	datasetJob := make([]entity.DatasetJob, 0)
	err = dao.DatasetJob.Ctx(ctx).Where("dataset_id = ?", dataset.Id).Scan(&datasetJob)
	assert.NoError(err)

	newRelData := datasetJob[:len(datasetJob)-1]

	_, err = dao.DatasetJob.Ctx(ctx).Delete("dataset_id = ?", dataset.Id)
	assert.NoError(err)

	_, err = dao.DatasetJob.Ctx(ctx).Insert(newRelData)
	assert.NoError(err)

	gen, err = checkNewDataGen(ctx, dataset)
	assert.NoError(err)
	assert.Equal(gen, true)

	_, err = dao.DatasetJob.Ctx(ctx).Delete("dataset_id = ?", dataset.Id)
	assert.NoError(err)

	_, err = dao.DatasetJob.Ctx(ctx).Insert(datasetJob)
	assert.NoError(err)

}

func Test_getTaskInfo(t *testing.T) {
	ctx := gctx.New()
	assert := assert.New(t)
	info, err := getTaskInfo(ctx, []uint{280, 281})
	assert.NoError(err)
	t.Log(info)
}

func Test_validateAndUpdateRelDataTaskId(t *testing.T) {

	ctx := context.Background()
	in := []model.RelDataDetail{
		{
			Type:   "task",
			TaskId: 792,
		},
		{
			Type:   "task",
			TaskId: 790,
		},
		{
			Type:  "job",
			JobId: 12663,
		},
		{
			Type:  "job",
			JobId: 12365,
		},
		{
			Type:  "job",
			JobId: 12363,
		},
	}
	got, err := validateRelData(ctx, in)
	assert.NoError(t, err)
	t.Log(got)
	in = append(in, []model.RelDataDetail{
		{
			Type:      "episode",
			EpisodeId: 927168,
		},
		{
			Type:      "episode",
			EpisodeId: 927220,
		},
	}...)
	got2, err := validateRelData(ctx, in)
	assert.NoError(t, err)
	t.Log(got2)

	in = append(in, model.RelDataDetail{
		Type:      "episode",
		EpisodeId: 913999,
	})
	got3, err := validateRelData(ctx, in)
	assert.NoError(t, err)
	t.Log(got3)
	in = append(in, model.RelDataDetail{
		Type:      "episode",
		EpisodeId: 913999,
	})
	_, err = validateRelData(ctx, in)
	t.Log(err)
	assert.Error(t, err, fmt.Sprintf("存在重复数据: type=%s, taskId=%d, jobId=%d, episodeId=%d", "episode", 0, 0, 913999))
}
