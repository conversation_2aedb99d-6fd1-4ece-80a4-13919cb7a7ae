// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	v1 "aim-data/api/collect/v1"
	"aim-data/internal/model"
	"aim-data/internal/model/entity"
	"context"
)

type (
	ICollect interface {
		// tenant-isolated
		InsertAssignment(ctx context.Context, jobID uint, assignmentType int) (uint, error)
		UpdateAssignment(ctx context.Context, in *v1.UpdateAssignmentReq) error
		// tenant-isolated
		ListAssignments(ctx context.Context, jobID uint) (*model.ListAssignmentOutput, error)
		// ListEpisodeByAssignmentID tenant-isolated 采集界面采集记录接口
		ListEpisodeByAssignmentID(ctx context.Context, in *model.ListEpisodeInput) (model.AssignmentDetail, error)
		GetAssignmentStat(ctx context.Context, input model.GetAssignmentStatInput) (*model.GetAssignmentStatOutput, error)
		// DownloadTaskFitOldVersion 根据是否有job_template判断新旧版本任务，来切换下载返回内容格式
		DownloadTaskFitOldVersion(ctx context.Context, taskId uint, jobIDs []uint, dataType string) (*model.DownloadTaskFitOldVersion, error)
		DownloadTask(ctx context.Context, taskId uint, taskName string, jobIDs []uint, episodeIds []uint64, dataType string) ([]model.DownloadOutput, error)
		CheckDownloadTask(ctx context.Context, taskId uint, jobIDs []uint, dataType string) (bool, error)
		// tenant-isolated
		InsertEpisode(ctx context.Context, in model.InsertEpisodeInput) (uint, error)
		UpdateEpisode(ctx context.Context, input model.UpdateEpisodeInput) error
		DeleteEpisode(ctx context.Context, episodeID uint) error
		GetEpisode(ctx context.Context, id uint) (*entity.Episode, error)
		// tenant-isolated
		GetCollectEpisode(ctx context.Context, in model.GetCollectEpisodeInput) (*model.GetCollectEpisodeOutput, error)
		GetEpisodeByID(ctx context.Context, episodeID uint) (*model.GetEpisodeOutput, error)
		// tenant-isolated
		BuildTaskJob(ctx context.Context, input model.BuildTaskJobInput) error
		ListTaskJob(ctx context.Context, input model.ListTaskJobInput) (*model.TaskJobDetail, error)
		SimpleListTaskJob(ctx context.Context, input model.SimpleListTaskJobInput) (*model.SimpleTaskJobOutput, error)
		DeleteJob(ctx context.Context, id uint) error
		BatchDeleteJob(ctx context.Context, ids []uint) error
		ListJobEpisodes(ctx context.Context, in model.ListEpisodeInput) (*model.ListEpisodeOutput, error)
		UpdateJob(ctx context.Context, input model.UpdateJobInput) error
		CloneJob(ctx context.Context, id uint) (uint, error)
		JobDetail(ctx context.Context, id uint) (*model.JobDetail, error)
		// 获取动作步骤详情：1变量 2物体 3原子能力
		GetActionStepDetail(ctx context.Context, job entity.Job) (model.JobActionStepDetail, error)
		SpecialFrameVisual(ctx context.Context, input model.FrameVisualInput) (*model.FrameVisual, error)
		// tenant-isolated
		GetStatInfo(ctx context.Context, in model.GetStatInfoInput) (*model.GetStatInfoOutput, error)
		// tenant-isolated
		GetStatTaskCollect(ctx context.Context, in model.GetStatTaskInput) (*model.GetStatTaskCollectOutput, error)
		// tenant-isolated
		GetStatTaskCheck(ctx context.Context, in model.GetStatTaskInput) (*model.GetStatTaskCheckOutput, error)
		// tenant-isolated
		GetStatDate(ctx context.Context, in model.GetStatDateInput) (*model.GetStatDateOutput, error)
		// tenant-isolated
		GetStatDateCSV(ctx context.Context, in model.GetStatDateInput) (*model.GetStatDateCSVOutput, error)
		// tenant-isolated
		GetStatUser(ctx context.Context, in model.GetStatUserInput) (*model.GetStatUserOutput, error)
		// tenant-isolated
		GetStatReason(ctx context.Context, in model.GetStatReasonInput) (*model.GetStatReasonOutput, error)
		// tenant-isolated
		GetStatReasonCSV(ctx context.Context, in model.GetStatReasonInput) (*model.GetStatDateCSVOutput, error)
		// tenant-isolated
		ValidDurationCSV(ctx context.Context, timeRange string) (*model.GetStatDateCSVOutput, error)
		GetStoreInfo(ctx context.Context, token string) (*model.CollectStoreInfo, error)
		// tenant-isolated
		CreateTask(ctx context.Context, input model.CreateTaskInput) (uint, error)
		// tenant-isolated
		UpdateTask(ctx context.Context, input model.UpdateTaskInput) error
		UpdateTaskStatus(ctx context.Context, input model.UpdateTaskInput) error
		UpdateTaskErrTag(ctx context.Context, input model.UpdateTaskInput) error
		DeleteTask(ctx context.Context, id uint) error
		// Deprecated: This function will be deprecated v0.7.
		ListTask(ctx context.Context, input model.ListTaskInput) (*model.ListTaskOutput, error)
		// tenant-isolated
		ListTaskV2(ctx context.Context, input model.ListTaskV2Input) (*model.ListTaskOutput, error)
		// tenant-isolated
		ListTaskV2WithoutProj(ctx context.Context, input model.ListTaskWithoutProjInput) (*model.ListTaskOutputWithoutProjOutput, error)
		SimpleListTask(ctx context.Context, input model.SimpleListTaskInput) (*model.SimpleTaskListOutput, error)
		AddTaskUsers(ctx context.Context, input []model.TaskUserInput) error
		UpdateTaskUser(ctx context.Context, id uint, status int) error
		// tenant-isolated
		ListTaskUser(ctx context.Context, input model.ListTaskUserInput) (*model.ListTaskUserOutput, error)
		// tenant-isolated
		ListTaskUserBaseInfoAll(ctx context.Context, input model.ListTaskUserBaseInfoAllInput) (*model.ListTaskUserBaseInfoAllOutput, error)
		// tenant-isolated
		IsCollectorInWhiteList(ctx context.Context, taskId uint, userID string, role uint) bool
		// tenant-isolated
		ListAllTask(ctx context.Context, status uint) (model.AllTaskOutput, error)
		GetTaskDetail(ctx context.Context, taskID uint) (*model.TaskDetailOutput, error)
		// tenant-isolated
		CopyTask(ctx context.Context, input model.CloneTaskInput) (uint, error)
		ListTaskForInternal(ctx context.Context, input model.ListTaskV2Input) (*model.ListTaskOutput, error)
		GetTaskStatusCount(ctx context.Context, req *v1.GetTaskStatusCountReq) (res *v1.GetTaskStatusCountRes, err error)
		// tenant-isolated
		CreateTaskResource(ctx context.Context, input model.TaskResourceParam) (uint, error)
		UpdateTaskResource(ctx context.Context, id uint, input model.TaskResourceParam) error
		DeleteTaskResource(ctx context.Context, id uint) error
		GetTaskResource(ctx context.Context, id uint) (*model.TaskResourceItem, error)
		// tenant-isolated
		ListTaskResource(ctx context.Context, input model.ListTaskResourceInput) (*model.ListTaskResourceOutput, error)
		// tenant-isolated
		GetAllTaskResource(ctx context.Context, taskID uint) ([]model.TaskResourceBaseInfo, error)
		UpdateStatInfoCache(ctx context.Context, in model.GetStatInfoInput) error
	}
)

var (
	localCollect ICollect
)

func Collect() ICollect {
	if localCollect == nil {
		panic("implement not found for interface ICollect, forgot register?")
	}
	return localCollect
}

func RegisterCollect(i ICollect) {
	localCollect = i
}
