// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"aidea-admin/internal/model/do"
	"aidea-admin/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/encoding/gjson"
)

type (
	ITenant interface {
		CallbackOnCreate(ctx context.Context, tenant *entity.Tenant) error
		ApplyToCasdoor(ctx context.Context, client CasdoorCtxClient, tenant *entity.Tenant) (*entity.Tenant, error)
		GetAll(ctx context.Context, opts ...ListOption) ([]*entity.Tenant, int, error)
		Get(ctx context.Context, nameOrID string) (*entity.Tenant, error)
		GetByName(ctx context.Context, name string) (*entity.Tenant, error)
		Create(ctx context.Context, casdoorClient CasdoorCtxClient, input *do.Tenant) (*entity.Tenant, error)
		Update(ctx context.Context, tenant *entity.Tenant, config *gjson.Json) (*entity.Tenant, error)
	}
)

var (
	localTenant ITenant
)

func Tenant() ITenant {
	if localTenant == nil {
		panic("implement not found for interface ITenant, forgot register?")
	}
	return localTenant
}

func RegisterTenant(i ITenant) {
	localTenant = i
}
