// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"aidea-admin/internal/consts"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
)

// Message is the golang structure for table message.
type Message struct {
	Id             int64                    `json:"id"              orm:"id"              description:"消息ID"`             // 消息ID
	Uuid           string                   `json:"uuid"            orm:"uuid"            description:"uuid"`             // uuid
	TenantId       int64                    `json:"tenant_id"       orm:"tenant_id"       description:"租户ID"`             // 租户ID
	NotificationId int64                    `json:"notification_id" orm:"notification_id" description:"通知ID"`             // 通知ID
	ChannelId      int64                    `json:"channel_id"      orm:"channel_id"      description:"渠道ID"`             // 渠道ID
	Sender         string                   `json:"sender"          orm:"sender"          description:"发送方"`              // 发送方
	Receiver       string                   `json:"receiver"        orm:"receiver"        description:"接收用户"`             // 接收用户
	Content        *gjson.Json              `json:"content"         orm:"content"         description:"消息内容"`             // 消息内容
	Expire         int64                    `json:"expire"          orm:"expire"          description:"超时失效时间戳"`          // 超时失效时间戳
	Status         consts.MessageStatusType `json:"status"          orm:"status"          description:"消息状态 0：待发送 1：已送达"` // 消息状态 0：待发送 1：已送达
	CreatedAt      *gtime.Time              `json:"created_at"      orm:"created_at"      description:"创建时间"`             // 创建时间
}
