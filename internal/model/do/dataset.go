// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Dataset is the golang structure of table dataset for DAO operations like Where/Data.
type Dataset struct {
	g.Meta           `orm:"table:dataset, do:true"`
	Id               interface{} //
	TenantId         interface{} // 租户id，默认为1
	DatasetName      interface{} // 数据集名称
	DatasetEngName   interface{} // 数据集名称（英）
	Source           interface{} // 数据来源：1:平台任务 2:文件导入
	DataType         interface{} // 数据类型 1:真实数据 2:仿真数据
	DataSize         interface{} // 单位：Byte
	Status           interface{} // 数据集状态 1：新建 2:创建中 3：创建完成  4:创建失败
	SyncStatus       interface{} // 数据集同步状态 5：同步中 6：同步完成 7：同步失败 8：暂停同步
	Remark           interface{} //
	EpisodeCnt       interface{} // 数据集下对应的episode数据量
	DatasetPath      interface{} // 数据集元数据存放地址
	DatasetSummary   interface{} // 数据集概览
	CreatorName      interface{} //
	CreateId         interface{} //
	UpdateId         interface{} //
	LockedUntil      *gtime.Time // 锁定截止时间
	RetryCnt         interface{} // 重试次数
	ReviewStatus     interface{} //
	ScheduleId       interface{} //
	FlowRunId        interface{} //
	LatestCheckpoint interface{} //
	SyncStrategyType interface{} // 1：手动同步 2：自动同步
	SyncInterval     interface{} // 自动同步间隔时间，单位：min
	SyncProgress     interface{} // 当前同步进度
	SyncDataRange    interface{} // 同步数据范围
	RelFile          interface{} // 平台创建类数据集是否关联文件,1:关联 2:非关联
	AutoAssociate    interface{} // 否自动关联任务下新增的子任务：1 自动，2 不自动
	ErrMsg           interface{} // 错误信息
	MetadataUpdate   interface{} // 标记该数据集关联的episode是否有变化的标记,1:有变化 2:无变化
	CreatedAt        *gtime.Time //
	UpdatedAt        *gtime.Time //
	DeletedAt        interface{} //
}
