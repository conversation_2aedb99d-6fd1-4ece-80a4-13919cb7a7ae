package model

import (
	"aim-data/internal/dao"
	"aim-data/internal/model/do"
	"aim-data/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type SyncDataAsset struct {
	RecordID int64    `json:"record_id"`
	Task     SyncTask `json:"task"`
	Meta     SyncMeta `json:"meta"`
}

type SyncMeta struct {
	TenantID   int64  `json:"tenant_id"`
	SourceConf string `json:"source_conf"`
}

type SyncTask struct {
	ID            uint               `json:"id"`
	Type          string             `json:"type"`
	DeviceTypeId  uint               `json:"device_type_id" orm:"device_type_id" description:"设备类型ID"`
	TenantID      int                `json:"tenant_id"`
	Name          string             `json:"name"`
	Content       string             `json:"content"`
	Status        int                `json:"status"`
	Config        string             `json:"config"`
	JobTemplate   SyncJobTemplate    `json:"job_template"`
	DeviceType    *SyncDeviceType    `json:"device_type"`
	Tags          *RelTag            `json:"tags"`
	TaskVariables []SyncTaskVariable `json:"task_variables"`
	TaskResources []SyncTaskResource `json:"task_resource"`
	Jobs          []SyncJob          `json:"jobs"`
}

type RelTag struct {
	DeviceTags       map[string][]entity.Tag `json:"device_tags"`
	TaskTags         map[string][]entity.Tag `json:"task_tags"`
	TaskResourceTags map[string][]entity.Tag `json:"task_resource_tags"`
	ParentTagDetail  map[string]*SyncTag     `json:"parent_tag_detail"`
}

type SyncJobTemplate struct {
	ActionLevel    int         `json:"action_level"     orm:"action_level"     description:"动作难度等级, 1:easy,2:middle,3:hard"`
	AutoChangeVar  int         `json:"auto_change_var"  orm:"auto_change_var"  description:"自动变更变量频率，缺省值为3"`
	InitialScene   string      `json:"initial_scene"    orm:"initial_scene"    description:"初始场景，混合文本，支持嵌套变量"`
	ActionStep     string      `json:"action_step"      orm:"action_step"      description:"动作步骤，混合文本，支持嵌套变量"`
	ActionStepType int         `json:"action_step_type" orm:"action_step_type" description:"动作步骤类型, 1:预定义固定步骤,2:随机采集"`
	Duration       int64       `json:"duration"         orm:"duration"         description:"required data collect duration time, default 5000ms"`
	MinDuration    int64       `json:"min_duration"     orm:"min_duration"     description:"required data collect min duration time, default 0ms"`
	Countdown      int64       `json:"countdown"        orm:"countdown"        description:"倒计时，任务开始前的准备时间, default 3000ms"`
	AbnormalRate   int         `json:"abnormal_rate"    orm:"abnormal_rate"    description:"非常规采集占比，10：表示任务要求有10%的非常规采集数据"`
	CreatedAt      *gtime.Time `json:"created_at"    orm:"created_at"    description:"Created Time"`
	UpdatedAt      *gtime.Time `json:"updated_at"    orm:"updated_at"    description:"Updated Time"`
}

type SyncDeviceType struct {
	Id                  uint        `json:"id"                    orm:"id"                    description:"ID"`
	Version             string      `json:"version"               orm:"version"               description:"device type version"`
	Name                string      `json:"name"                  orm:"name"                  description:"device type name"`
	TaskCollectOntology uint        `json:"task_collect_ontology" orm:"task_collect_ontology" description:"采集本体-标签ID"`
	TaskEndKind         uint        `json:"task_end_kind"         orm:"task_end_kind"         description:"末端类型-标签ID"`
	TaskEndKindRight    uint        `json:"task_end_kind_right"   orm:"task_end_kind_right"   description:"末端类型右(缺省值复用左末端值)-标签ID"`
	Urdf                string      `json:"urdf"                  orm:"urdf"                  description:"设备urdf"`
	Img                 string      `json:"img"                   orm:"img"                   description:"设备图片"`
	ChannelConfig       string      `json:"channel_config"        orm:"channel_config"        description:"相机通道配置"`
	VisualConfig        string      `json:"visual_config"         orm:"visual_config"         description:"可视化配置"`
	CreatedAt           *gtime.Time `json:"created_at"            orm:"created_at"            description:"Created Time"`
	UpdatedAt           *gtime.Time `json:"updated_at"            orm:"updated_at"            description:"Updated Time"`
}

type SyncTag struct {
	Id     uint     `json:"id"         orm:"id"         description:"ID"`
	Code   string   `json:"code"       orm:"code"       description:"tag code"`
	Label  string   `json:"label"      orm:"label"      description:"tag label"`
	Pid    int      `json:"pid"        orm:"pid"        description:"tag pid"`
	Level  int      `json:"level"      orm:"level"      description:"tag level"`
	Status int      `json:"status"     orm:"status"     description:"tag status, 1:TagTypeSystemPredefinedCannotAdd, 2:TagTypeNormalBoundCannotDelete, 3:TagTypeNormalBoundCanDelete 4:TagTypeSystemPredefinedCanAdd"`
	Parent *SyncTag `json:"parent"     orm:"parent"     description:"parent tag"`
}

type SyncJob struct {
	Id             uint          `json:"id"`
	Description    string        `json:"description"     orm:"description"     description:"job description"`
	ActionLevel    int           `json:"action_level"    orm:"action_level"    description:"动作难度等级, 1:easy,2:middle,3:hard"`
	AutoChangeVar  int           `json:"auto_change_var" orm:"auto_change_var" description:"自动变更变量频率，缺省值为3"`
	Countdown      int64         `json:"countdown"       orm:"countdown"       description:"倒计时，任务开始前的准备时间, default 3000ms"`
	AbnormalRate   int           `json:"abnormal_rate"   orm:"abnormal_rate"   description:"非常规采集占比，10：表示任务要求有10%的非常规采集数据"`
	Duration       int64         `json:"duration"        orm:"duration"        description:"required data collect duration time, default 5000ms"`
	RequiredRepeat int           `json:"required_repeat" orm:"required_repeat" description:"required data collect repeat count"`
	RequiredMember int           `json:"required_member" orm:"required_member" description:"required data collector member count"`
	RequiredVerify int           `json:"required_verify" orm:"required_verify" description:"required data collector member count"`
	Extra          string        `json:"extra"           orm:"extra"           description:"task extra config"`
	CreatedAt      *gtime.Time   `json:"created_at"    orm:"created_at"    description:"Created Time"`
	UpdatedAt      *gtime.Time   `json:"updated_at"    orm:"updated_at"    description:"Updated Time"`
	Episodes       []SyncEpisode `json:"episodes"`
}

type SyncEpisode struct {
	Id         uint64       `json:"id"            orm:"id"            description:"ID"`
	JobId      uint         `json:"job_id"        orm:"job_id"        description:"job ID"`
	DataType   int          `json:"data_type"     orm:"data_type"     description:"数据类型, 1:normal,2:abnormal"`
	Status     int          `json:"status"        orm:"status"        description:"episode status, 0:NotUploaded, 1:Uploaded"`
	SnCode     string       `json:"sn_code"       orm:"sn_code"       description:"data collection machine sn code"`
	Size       int64        `json:"size"          orm:"size"          description:"raw data size"`
	Duration   int64        `json:"duration"      orm:"duration"      description:"episode duration"`
	Text       string       `json:"text"          orm:"text"          description:"场景文本"`
	Path       string       `json:"path"          orm:"path"          description:"data collection path"`
	RawPath    string       `json:"raw_path"      orm:"raw_path"      description:"原始数据路径"`
	ReviewPath string       `json:"review_path"   orm:"review_path"   description:"data web view path"`
	FramedPath string       `json:"framed_path"   orm:"framed_path"   description:"组帧后的路径"`
	Extra      string       `json:"extra"         orm:"extra"         description:"episode extra info"`
	CreatedAt  *gtime.Time  `json:"created_at"    orm:"created_at"    description:"Created Time"`
	UpdatedAt  *gtime.Time  `json:"updated_at"    orm:"updated_at"    description:"Updated Time"`
	Reviews    []SyncReview `json:"reviews"`
}

type SyncReview struct {
	EpisodeId             uint64      `json:"episode_id"`
	Status                int         `json:"status"                  orm:"status"                  description:"episode status, 6:CheckPass, 7:CheckNotPass， 99:Invalid"`
	UsabilityScore        int         `json:"usability_score"         orm:"usability_score"         description:"可使用程度分类评分:1-优秀，2-可接受，3-差，默认-1未分类"`
	OverallScore          int         `json:"overall_score"           orm:"overall_score"           description:"整体评分:1-5, 默认-1未评分"`
	ErrorCountScore       int         `json:"error_count_score"       orm:"error_count_score"       description:"错误量评分:1-5, 默认-1未评分"`
	SmoothnessScore       int         `json:"smoothness_score"        orm:"smoothness_score"        description:"流畅度评分:1-5, 默认-1未评分"`
	ValidData             int         `json:"valid_data"              orm:"valid_data"              description:"1:valid, 2:invalid"`
	ValidAction           int         `json:"valid_action"            orm:"valid_action"            description:"1:valid, 2:invalid"`
	Comment               string      `json:"comment"                 orm:"comment"                 description:"review comment"`
	KeyFrame              string      `json:"key_frame"               orm:"key_frame"               description:"review key frame"`
	ActionStepCalibration string      `json:"action_step_calibration" orm:"action_step_calibration" description:"动作步骤校准"`
	CreatedAt             *gtime.Time `json:"created_at"              orm:"created_at"              description:"Created Time"`
	UpdatedAt             *gtime.Time `json:"updated_at"              orm:"updated_at"              description:"Updated Time"`
}

type SyncTaskVariable struct {
	Id          uint        `json:"id"           orm:"id"           description:"ID"`
	TaskId      uint        `json:"task_id"      orm:"task_id"      description:"task ID"`
	JobId       uint        `json:"job_id"       orm:"job_id"       description:"job ID，未创建实例任务时，job id为0"`
	Type        int         `json:"type"         orm:"type"         description:"变量类型, 1:物体类型, 2:赋值类型, 3:场景变量"`
	Name        string      `json:"name"         orm:"name"         description:"变量名，缺省值为UUID"`
	DisplayName string      `json:"display_name" orm:"display_name" description:"变量显示名，缺省值为空"`
	Values      string      `json:"values"       orm:"values"       description:"变量枚举值，物体类型为物体IDs"`
	Status      int         `json:"status"       orm:"status"       description:"变量状态, 1:启动, 2:禁用"`
	CreatedAt   *gtime.Time `json:"created_at"   orm:"created_at"   description:"Created Time"`
	UpdatedAt   *gtime.Time `json:"updated_at"   orm:"updated_at"   description:"Updated Time"`
}

type SyncTaskResource struct {
	Id      uint   `json:"id"           orm:"id"           description:"ID"`
	Name    string `json:"name"         orm:"name"         description:"物品的名称"`
	Img     string `json:"img"          orm:"img"          description:"物品的图像"`
	Content string `json:"content"      orm:"content"      description:"物品的描述内容，支持富文本"`
	Extra   string `json:"extra"        orm:"extra"        description:"物品备注等补充信息"`
	TagIDs  []int  `json:"tag_ids"      orm:"tag_ids"      description:"物品的标签id"`
}

type EpisodePathMapping struct {
	Origin *EpisodePath `json:"origin"`
	Target *EpisodePath `json:"target"`
}

type EpisodePath struct {
	Path       string `json:"path"`
	RawPath    string `json:"raw_path"      orm:"raw_path"      description:"原始数据路径"`
	ReviewPath string `json:"review_path"   orm:"review_path"   description:"data web view path"`
	FramedPath string `json:"framed_path"   orm:"framed_path"   description:"组帧后的路径"`
}

type TaskPathMapping struct {
	Origin string `json:"origin"`
	Target string `json:"target"`
}

// GetTagIdChain 根据tagID递归获取所有父tagID，直到pid为0，返回tagID链路（从子到父，Parent字段递归）
func GetTagIdChain(ctx context.Context, tagIDs []uint) (map[string]SyncTag, error) {
	result := make(map[string]SyncTag)
	for _, tagID := range tagIDs {
		var chain *SyncTag
		curID := tagID
		for {
			var tag entity.Tag
			err := dao.Tag.Ctx(ctx).Where(do.Tag{Id: curID}).Scan(&tag)
			if err != nil {
				return nil, err
			}
			if tag.Id == 0 {
				break
			}
			t := &SyncTag{
				Id:     tag.Id,
				Code:   tag.Code,
				Label:  tag.Label,
				Pid:    tag.Pid,
				Level:  tag.Level,
				Status: tag.Status,
				Parent: chain,
			}
			chain = t
			if tag.Pid == 0 {
				break
			}
			curID = uint(tag.Pid)
		}
		if chain != nil {
			result[gconv.String(tagID)] = *chain
		}
	}
	return result, nil
}
