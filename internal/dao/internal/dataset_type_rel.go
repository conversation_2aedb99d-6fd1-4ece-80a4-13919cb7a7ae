// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DatasetTypeRelDao is the data access object for the table dataset_type_rel.
type DatasetTypeRelDao struct {
	table   string                // table is the underlying table name of the DAO.
	group   string                // group is the database configuration group name of the current DAO.
	columns DatasetTypeRelColumns // columns contains all the column names of Table for convenient usage.
}

// DatasetTypeRelColumns defines and stores column names for the table dataset_type_rel.
type DatasetTypeRelColumns struct {
	DatasetId  string //
	DataTypeId string // 1:真实数据 2:仿真数据
}

// datasetTypeRelColumns holds the columns for the table dataset_type_rel.
var datasetTypeRelColumns = DatasetTypeRelColumns{
	DatasetId:  "dataset_id",
	DataTypeId: "data_type_id",
}

// NewDatasetTypeRelDao creates and returns a new DAO object for table data access.
func NewDatasetTypeRelDao() *DatasetTypeRelDao {
	return &DatasetTypeRelDao{
		group:   "default",
		table:   "dataset_type_rel",
		columns: datasetTypeRelColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DatasetTypeRelDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DatasetTypeRelDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DatasetTypeRelDao) Columns() DatasetTypeRelColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DatasetTypeRelDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DatasetTypeRelDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DatasetTypeRelDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
