// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TemplateDao is the data access object for the table template.
type TemplateDao struct {
	table   string          // table is the underlying table name of the DAO.
	group   string          // group is the database configuration group name of the current DAO.
	columns TemplateColumns // columns contains all the column names of Table for convenient usage.
}

// TemplateColumns defines and stores column names for the table template.
type TemplateColumns struct {
	Id         string // ID
	TenantId   string // 租户id，默认为1
	ApiVersion string // template api version
	Kind       string // template kind
	Name       string // template name
	Template   string // template yaml
	Owner      string // template owner
	OwnerId    string // template owner
	CreatedAt  string // Created Time
	UpdatedAt  string // Updated Time
	DeletedAt  string // Deleted Time
}

// templateColumns holds the columns for the table template.
var templateColumns = TemplateColumns{
	Id:         "id",
	TenantId:   "tenant_id",
	ApiVersion: "api_version",
	Kind:       "kind",
	Name:       "name",
	Template:   "template",
	Owner:      "owner",
	OwnerId:    "owner_id",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
	DeletedAt:  "deleted_at",
}

// NewTemplateDao creates and returns a new DAO object for table data access.
func NewTemplateDao() *TemplateDao {
	return &TemplateDao{
		group:   "default",
		table:   "template",
		columns: templateColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TemplateDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TemplateDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TemplateDao) Columns() TemplateColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TemplateDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TemplateDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TemplateDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
