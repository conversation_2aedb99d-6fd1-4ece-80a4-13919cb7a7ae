// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TaskDao is the data access object for the table task.
type TaskDao struct {
	table   string      // table is the underlying table name of the DAO.
	group   string      // group is the database configuration group name of the current DAO.
	columns TaskColumns // columns contains all the column names of Table for convenient usage.
}

// TaskColumns defines and stores column names for the table task.
type TaskColumns struct {
	Id           string // ID
	TenantId     string // 租户id，默认为1
	Name         string // task name
	Type         string // task type
	Content      string // task description content, support enriched text
	Status       string // task status, 1:active, 2:inactive
	DeviceTypeId string // 设备类型ID
	Creator      string // 创建者账号
	CreatorName  string // 创建者名称
	CreatorId    string // 创建者ID
	Updater      string // 更新者账号
	UpdaterName  string // 更新者名称
	UpdaterId    string // 更新者ID
	Config       string // task config content, support enriched text
	Instruction  string  //任务指令
	CreatedAt    string // Created Time
	UpdatedAt    string // Updated Time
	DeletedAt    string // Deleted Timestamp
}

// taskColumns holds the columns for the table task.
var taskColumns = TaskColumns{
	Id:           "id",
	TenantId:     "tenant_id",
	Name:         "name",
	Type:         "type",
	Content:      "content",
	Status:       "status",
	DeviceTypeId: "device_type_id",
	Creator:      "creator",
	CreatorName:  "creator_name",
	CreatorId:    "creator_id",
	Updater:      "updater",
	UpdaterName:  "updater_name",
	UpdaterId:    "updater_id",
	Config:       "config",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewTaskDao creates and returns a new DAO object for table data access.
func NewTaskDao() *TaskDao {
	return &TaskDao{
		group:   "default",
		table:   "task",
		columns: taskColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TaskDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TaskDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TaskDao) Columns() TaskColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TaskDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TaskDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TaskDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
