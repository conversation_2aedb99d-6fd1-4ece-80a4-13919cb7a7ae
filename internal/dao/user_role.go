// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"aidea-admin/internal/dao/internal"
	"aidea-admin/internal/model/do"
	"aidea-admin/internal/model/entity"
	"aidea-admin/internal/utils"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
)

// userRoleDao is the data access object for the table user_role.
// You can define custom methods on it to extend its functionality as needed.
type userRoleDao struct {
	*internal.UserRoleDao
}

var (
	// UserRole is a globally accessible object for table user_role operations.
	UserRole = userRoleDao{internal.NewUserRoleDao()}
)

// Add your custom methods and functionality below.

func (d *userRoleDao) WithTenant(tenant *entity.Tenant) gdb.ModelHandler {
	return func(m *gdb.Model) *gdb.Model {
		return m.Where(d.Columns().TenantName, tenant.Name)
	}
}

func (d *userRoleDao) WithRole(role *entity.Role) gdb.ModelHandler {
	return func(m *gdb.Model) *gdb.Model {
		return m.Where(d.Columns().RoleName, role.Name).Where(d.Columns().TenantName, role.TenantName)
	}
}

func (d *userRoleDao) GetRolesForUsers(ctx context.Context, tenantName string, userNames []string) (map[string][]*entity.UserRole, error) {
	userRoles := make([]*entity.UserRole, 0)
	if err := d.Ctx(ctx).Where(d.Columns().TenantName, tenantName).WhereIn(d.Columns().UserName, userNames).Scan(&userRoles); err != nil {
		return nil, err
	}
	userRolesMap := make(map[string][]*entity.UserRole)
	for _, ur := range userRoles {
		userRolesMap[ur.UserName] = append(userRolesMap[ur.UserName], ur)
	}
	return userRolesMap, nil
}

func (d *userRoleDao) GetRolesForUser(ctx context.Context, tenant *entity.Tenant, userName string) ([]*entity.UserRole, error) {
	userRoles := make([]*entity.UserRole, 0)
	if err := d.Ctx(ctx).Handler(d.WithTenant(tenant)).Where(d.Columns().UserName, userName).Scan(&userRoles); err != nil {
		return nil, err
	}
	return userRoles, nil
}

func (d *userRoleDao) AddRolesForUser(ctx context.Context, tenant *entity.Tenant, userName string, roles []*entity.UserRole) error {
	if _, err := d.Ctx(ctx).Insert(utils.Map(roles, func(role *entity.UserRole) *do.UserRole {
		return &do.UserRole{
			TenantName: tenant.Name,
			UserName:   userName,
			RoleName:   role.RoleName,
		}
	})); err != nil {
		return err
	}
	return nil
}

func (d *userRoleDao) DeleteRolesForUser(ctx context.Context, tenant *entity.Tenant, userName string, roles []*entity.UserRole) error {
	if _, err := d.Ctx(ctx).
		Handler(d.WithTenant(tenant)).
		Where(d.Columns().UserName, userName).
		WhereIn(d.Columns().RoleName, utils.Map(roles, func(role *entity.UserRole) string {
			return role.RoleName
		})).Delete(); err != nil {
		return err
	}
	return nil
}
