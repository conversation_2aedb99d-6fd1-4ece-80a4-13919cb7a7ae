// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTask = "task"

// Task mapped from table <task>
type Task struct {
	ID           int32     `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true;comment:ID" json:"id"`                                               // ID
	TenantID     int32     `gorm:"column:tenant_id;type:int unsigned;not null;index:idx_task_tenant_id,priority:1;default:1;comment:租户id，默认为1" json:"tenant_id"` // 租户id，默认为1
	Name         string    `gorm:"column:name;type:varchar(63);not null;index:idx_name,priority:1;comment:task name" json:"name"`                                // task name
	Type         string    `gorm:"column:type;type:varchar(63);not null;default:GENIE;comment:task type" json:"type"`                                            // task type
	Content      string    `gorm:"column:content;type:longtext;comment:task description content, support enriched text" json:"content"`                          // task description content, support enriched text
	Status       int32     `gorm:"column:status;type:tinyint;not null;default:1;comment:task status, 1:active, 2:inactive" json:"status"`                        // task status, 1:active, 2:inactive
	DeviceTypeID int32     `gorm:"column:device_type_id;type:int unsigned;not null" json:"device_type_id"`
	Creator      string    `gorm:"column:creator;type:varchar(127);comment:创建者账号" json:"creator"`                                                 // 创建者账号
	CreatorName  string    `gorm:"column:creator_name;type:varchar(127);comment:创建者名称" json:"creator_name"`                                       // 创建者名称
	CreatorID    string    `gorm:"column:creator_id;type:varchar(127);comment:创建者ID" json:"creator_id"`                                           // 创建者ID
	Updater      string    `gorm:"column:updater;type:varchar(127);comment:更新者账号" json:"updater"`                                                 // 更新者账号
	UpdaterName  string    `gorm:"column:updater_name;type:varchar(127);comment:更新者名称" json:"updater_name"`                                       // 更新者名称
	UpdaterID    string    `gorm:"column:updater_id;type:varchar(127);comment:更新者ID" json:"updater_id"`                                           // 更新者ID
	Config       string    `gorm:"column:config;type:longtext;comment:task config content, support enriched text" json:"config"`                  // task config content, support enriched text
	Instruction  string    `gorm:"column:instruction;type:longtext;comment:任务指令" json:"instruction"`
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:Created Time" json:"created_at"`     // Created Time
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:Updated Time" json:"updated_at"`     // Updated Time
	DeletedAt    int64     `gorm:"column:deleted_at;type:bigint;index:idx_name,priority:2;default:0;comment:Deleted Timestamp" json:"deleted_at"` // Deleted Timestamp
}

// TableName Task's table name
func (*Task) TableName() string {
	return TableNameTask
}
