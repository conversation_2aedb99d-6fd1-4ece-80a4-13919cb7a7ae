// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"aidea-admin/internal/dao/internal"
	"aidea-admin/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
)

// internalNotificationDao is an internal type for wrapping the internal DAO implementation.
type internalNotificationDao = *internal.NotificationDao

// notificationDao is the data access object for the table notification.
// You can define custom methods on it to extend its functionality as needed.
type notificationDao struct {
	internalNotificationDao
}

var (
	// Notification is a globally accessible object for table notification operations.
	Notification = notificationDao{
		internal.NewNotificationDao(),
	}
)

// Add your custom methods and functionality below.

func (d *notificationDao) GetByUUID(ctx context.Context, uuid string) (*entity.Notification, error) {
	var notification *entity.Notification
	if err := d.Ctx(ctx).Where("uuid=?", uuid).Scan(&notification); err != nil {
		return nil, err
	}
	if notification == nil {
		return nil, gerror.New("notification not found")
	}
	return notification, nil
}
