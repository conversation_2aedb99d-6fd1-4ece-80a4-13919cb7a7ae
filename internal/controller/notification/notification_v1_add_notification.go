package notification

import (
	"context"

	v1 "aidea-admin/api/notification/v1"
	"aidea-admin/internal/model/entity"
	"aidea-admin/internal/service"

	"github.com/gogf/gf/v2/os/gtime"
)

func (c *ControllerV1) AddNotification(ctx context.Context, req *v1.AddNotificationReq) (res *v1.AddNotificationRes, err error) {
	tenant, err := service.Tenant().GetByName(ctx, req.Tenant)
	if err != nil {
		return nil, err
	}

	err = service.Notification().AddNotification(ctx, &entity.Notification{
		Uuid:      req.UUID,
		TenantId:  tenant.Id,
		Content:   req.Content,
		Status:    0,
		CreatedAt: gtime.Now(),
	})
	return &v1.AddNotificationRes{
		UUID: req.UUID,
	}, err
}
