package tenant

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gjson"

	v1 "aidea-admin/api/tenant/v1"
	"aidea-admin/internal/model/do"
	"aidea-admin/internal/service"
)

func (c *ControllerV1) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	tenant, err := service.Tenant().Create(ctx, service.CasdoorClient(), &do.Tenant{
		Name:   req.Name,
		Config: gjson.New(req.Config),
	})
	if err != nil {
		return nil, err
	}
	return &v1.CreateRes{
		Id: tenant.Id,
	}, nil
}
