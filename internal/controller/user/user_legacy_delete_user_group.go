package user

import (
	"context"

	"aidea-admin/api/user/legacy"
	"aidea-admin/internal/service"
)

func (c *ControllerLegacy) DeleteUserGroup(ctx context.Context, req *legacy.DeleteUserGroupReq) (res *legacy.DeleteUserGroupRes, err error) {
	if err := service.User().DeleteUserGroup(ctx, req.CurrentUser.Tenant, int64(req.ID)); err != nil {
		return nil, err
	}
	return &legacy.DeleteUserGroupRes{}, nil
}
