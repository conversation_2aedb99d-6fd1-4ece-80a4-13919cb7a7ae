package collect

import (
	"context"

	"aim-data/internal/model"
	"aim-data/internal/service"

	v1 "aim-data/api/collect/v1"
)

func (c *ControllerV1) CreateEpisode(ctx context.Context, req *v1.CreateEpisodeReq) (res *v1.CreateEpisodeRes, err error) {
	nCtx, err := bindTenantFromToken(ctx, req.Token)
	if err != nil {
		return nil, err
	}
	id, err := service.Collect().InsertEpisode(nCtx, model.InsertEpisodeInput{
		Token:    req.Token,
		SNCode:   req.SNCode,
		Path:     req.Path,
		Status:   req.Status,
		DataType: req.DataType,
		Size:     req.Size,
		Duration: req.Duration,
		Text:     req.Text,
	})
	if err != nil {
		return nil, err
	}
	return &v1.CreateEpisodeRes{ID: id}, nil
}
