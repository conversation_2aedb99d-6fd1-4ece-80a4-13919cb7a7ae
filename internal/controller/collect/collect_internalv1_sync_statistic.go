// Package collect provides internal v1 controllers for collection services.
package collect

import (
	"aim-data/api/collect/internalv1"
	"aim-data/internal/model"
	"aim-data/internal/service"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
)

// SyncStatistic handles the internal statistic synchronization request.
func (c *ControllerInternalv1) SyncStatistic(ctx context.Context, req *internalv1.SyncStatisticReq) (res *internalv1.SyncStatisticRes, err error) {
	// 调用 service 层的更新逻辑
	err = service.Collect().UpdateStatInfoCache(ctx, model.GetStatInfoInput{
		TaskID: req.TaskID,
		JobID:  req.JobId,
	})
	if err != nil {
		return nil, gerror.Wrap(err, "Statistic synchronization failed")
	}

	// 返回响应
	return &internalv1.SyncStatisticRes{}, nil
}
