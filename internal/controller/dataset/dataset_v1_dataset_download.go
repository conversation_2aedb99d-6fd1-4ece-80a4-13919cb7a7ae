package dataset

import (
	v1 "aim-data/api/dataset/v1"
	"aim-data/internal/model"
	"aim-data/internal/service"
	"context"

	"github.com/jinzhu/copier"
)

func (c *ControllerV1) DatasetDownload(ctx context.Context, req *v1.DatasetDownloadReq) (res *v1.DatasetDownloadRes, err error) {
	input := &model.ReqId{}
	if err := copier.Copy(input, req); err != nil {
		return nil, err
	}

	output, err := service.Dataset().Download(ctx, input)
	if err != nil {
		return nil, err
	}

	res = &v1.DatasetDownloadRes{}
	if err := copier.Copy(res, output); err != nil {
		return nil, err
	}

	return res, nil
}
