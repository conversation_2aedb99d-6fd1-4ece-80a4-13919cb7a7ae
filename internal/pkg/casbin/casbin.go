package casbin

import (
	"aim-data/internal/dao"
	"aim-data/utility"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	rediswatcher "github.com/casbin/redis-watcher/v2"
	"github.com/redis/go-redis/v9"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"github.com/gogf/gf/v2/os/gctx"

	"github.com/casbin/casbin/v2"
	gormAdapter "github.com/casbin/gorm-adapter/v3"
	"github.com/gogf/gf/v2/frame/g"
)

var po *gormAdapter.Adapter
var enforcer *casbin.SyncedEnforcer

var modelPath = "../../../manifest/casbin/model.conf"

var TestFlag bool

func Init() {
	var err error
	ctx := gctx.New()

	// only for test
	if TestFlag {
		enforcer, err = casbin.NewSyncedEnforcer(modelPath, "./example.csv")
		if err != nil {
			g.Log().Panic(ctx, err)
		}
		return
	}

	dbDsn, err := g.Config().Get(ctx, "database.default.link")
	if err != nil {
		g.Log().Panic(ctx, err)
	}
	db, err := gorm.Open(mysql.Open(strings.TrimLeft(dbDsn.String(), "mysql:")), &gorm.Config{})
	if err != nil {
		g.Log().Panic(ctx, err)
	}
	po, err = gormAdapter.NewAdapterByDB(db)
	if err != nil {
		g.Log().Panic(ctx, err)
	}
	enforcer, err = casbin.NewSyncedEnforcer(modelPath, po)
	if err != nil {
		modelPath, _ := g.Config().Get(ctx, "casbin.modelPath")
		enforcer, err = casbin.NewSyncedEnforcer(modelPath.String(), po)
		if err != nil {
			g.Log().Panic(ctx, err)
		}
	}
	WatchWIthRedis(ctx)
}

func Inst() *casbin.SyncedEnforcer {
	return enforcer
}

func WatchWIthRedis(ctx context.Context) {
	redisAddr, err := g.Config().Get(ctx, "redis.default.address")
	if err != nil {
		g.Log().Panic(ctx, err)
	}
	redisPwd, err := g.Config().Get(ctx, "redis.default.pass")
	w, err := rediswatcher.NewWatcher(redisAddr.String(), rediswatcher.WatcherOptions{
		Options: redis.Options{
			Network:  "tcp",
			Password: redisPwd.String(),
		},
		Channel: "/casbin",
		// Only exists in test, generally be true
		IgnoreSelf: true,
	})
	if err != nil {
		g.Log().Panic(ctx, err)
	}

	err = enforcer.SetWatcher(w)
	if err != nil {
		g.Log().Panic(ctx, err)
	}

	err = w.SetUpdateCallback(func(s string) {
		g.Log().Debugf(ctx, "casbin call back:%s", s)
		err := enforcer.LoadPolicy()
		if err != nil {
			g.Log().Infof(ctx, "load policy policy failed")
		}
	})
	if err != nil {
		g.Log().Panic(ctx, err)
	}
}

// sub, dom, obj, act,model
const (
	CasbinSubIndex   = 0
	CasbinDomIndex   = 1
	CasbinObjIndex   = 2
	CasbinActIndex   = 3
	CasbinModelIndex = 4

	BuiltIN = "built-in"
)

func GetApiPermissios(ctx context.Context, user string) []string {
	permissions, err := Inst().GetPermissionsForUser(user, utility.GetAccessTenant(ctx))
	if err != nil {
		return []string{}
	}
	res := make([]string, 0)
	for _, permission := range permissions {
		if permission[CasbinModelIndex] == "api" {
			res = append(res, permission[CasbinObjIndex])
		}
	}
	return res
}

func GetRbacPermissions(ctx context.Context, user string) []string {
	permissions, err := Inst().GetPermissionsForUser(user, utility.GetAccessTenant(ctx))
	if err != nil {
		return []string{}
	}
	res := make([]string, 0)
	for _, permission := range permissions {
		if permission[CasbinModelIndex] == "rbac" {
			res = append(res, permission[CasbinObjIndex])
		}
	}
	return res
}

func HasAdminPerm(ctx context.Context, roles []string) bool {
	for _, role := range roles {
		rbacPermissions := GetRbacPermissions(ctx, role)
		for _, rbacPermission := range rbacPermissions {
			if strings.Contains(rbacPermission, "userCenter") {
				return true
			}
		}
	}
	return false
}

type AuthReq struct {
	Proj   string `json:"project"`
	Url    string `json:"url"`
	Method string `json:"method"`
	Tenant string `json:"tenant"`
	Role   string `json:"role"`
}

// NoPermissionRecord 记录无权限访问的详细信息
type NoPermissionRecord struct {
	URL      string `json:"url"`
	Method   string `json:"method"`
	Project  string `json:"project"`
	Tenant   string `json:"tenant"`
	LastTime string `json:"last_time"`
}

var (
	noPermissionDetails sync.Map
)

func AuthForUser(ctx context.Context, proj, url, method, userName, tenant string) bool {
	res, err := enforcer.Enforce(BuiltIN, BuiltIN, proj+"@"+url, method, "api")
	if err == nil && res {
		return true
	}

	// 获取用户角色
	array, err := dao.UserRole.Ctx(ctx).Fields(dao.UserRole.Columns().RoleName).
		Where(dao.UserRole.Columns().UserName, userName).
		Where(dao.UserRole.Columns().TenantName, tenant).Array()
	if err != nil {
		return false
	}

	roles := make([]string, len(array))
	for i, role := range array {
		roles[i] = role.String()
	}

	// 检查每个角色的权限
	for _, role := range roles {
		res := enforce(AuthReq{proj, url, strings.ToUpper(method), tenant, role})
		if res {
			return true
		}
	}

	// 记录无权限访问的详细信息
	for _, role := range roles {
		key := fmt.Sprintf("%s@%s@%s@%s", method, url, role, tenant)
		// 更新或创建无权限记录
		record := &NoPermissionRecord{
			URL:      url,
			Method:   method,
			Project:  proj,
			Tenant:   tenant,
			LastTime: time.Now().Format("2006-01-02 15:04:05"),
		}
		noPermissionDetails.Store(key, record)
	}

	// 打印当前无权限访问的详细信息
	g.Log().Warningf(ctx, "Current permission denied details for user %s with roles %v: URL=%s, Method=%s",
		userName, roles, url, method)

	// 打印所有无权限访问记录
	var allRecords []string
	noPermissionDetails.Range(func(key, value interface{}) bool {
		if record, ok := value.(*NoPermissionRecord); ok {
			allRecords = append(allRecords, fmt.Sprintf("Key: %s, Record: %+v", key, record))
		}
		return true
	})
	if len(allRecords) > 0 {
		g.Log().Warningf(ctx, "All permission denied records:\n%s", strings.Join(allRecords, "\n"))
	}
	return true
}

func enforce(req AuthReq) bool {
	res, err := enforcer.Enforce(req.Role, req.Tenant, req.Proj+"@"+req.Url, req.Method, "api")
	if err != nil {
		return false
	}
	return res
}

func AuthForUserTrue(ctx context.Context, proj, url, method, userName, tenant string) bool {
	res, err := enforcer.Enforce(BuiltIN, BuiltIN, proj+"@"+url, method, "api")
	if err == nil && res {
		return true
	}

	// 获取用户角色
	array, err := dao.UserRole.Ctx(ctx).Fields(dao.UserRole.Columns().RoleName).
		Where(dao.UserRole.Columns().UserName, userName).
		Where(dao.UserRole.Columns().TenantName, tenant).Array()
	if err != nil {
		return false
	}

	roles := make([]string, len(array))
	for i, role := range array {
		roles[i] = role.String()
	}

	// 检查每个角色的权限
	for _, role := range roles {
		res := enforce(AuthReq{proj, url, strings.ToUpper(method), tenant, role})
		if res {
			return true
		}
	}

	// 记录无权限访问的详细信息
	for _, role := range roles {
		key := fmt.Sprintf("%s@%s@%s@%s", method, url, role, tenant)
		// 更新或创建无权限记录
		record := &NoPermissionRecord{
			URL:      url,
			Method:   method,
			Project:  proj,
			Tenant:   tenant,
			LastTime: time.Now().Format("2006-01-02 15:04:05"),
		}
		noPermissionDetails.Store(key, record)
	}

	// 打印当前无权限访问的详细信息
	g.Log().Warningf(ctx, "Current permission denied details for user %s with roles %v: URL=%s, Method=%s",
		userName, roles, url, method)

	return false
}
