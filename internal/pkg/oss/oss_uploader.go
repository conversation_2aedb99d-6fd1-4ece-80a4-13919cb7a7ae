package oss

import (
	"context"
	"os"
	"path/filepath"

	"aim-data/utility"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type uploader struct {
	up  *manager.Uploader
	ctx context.Context
}

type IUploader interface {
	UploadPath(localPath, s3path string) (int64, error)
}

func UploaderSvc(ctx context.Context, opts ...Option) IUploader {
	return &uploader{up: manager.NewUploader(s3Client(ctx, opts...)), ctx: ctx}
}

func (u *uploader) UploadPath(localPath, s3path string) (int64, error) {
	g.Log().Debugf(u.ctx, "Start upload local path %s to  %s", localPath, s3path)
	defer utility.TimeCost("Total")(u.ctx)
	walker := make(fileWalk)
	go func() {
		// Gather the files to upload by walking the path recursively
		if err := filepath.Walk(localPath, walker.Walk); err != nil {
			g.Log().Fatalf(u.ctx, "Walk failed: %v", err)
		}
		close(walker)
	}()

	bucket, prefix, err := utility.S3PathParse(s3path)
	if err != nil {
		return 0, err
	}
	var totalSize int64
	for path := range walker {
		size, err := u.uploadPath(localPath, path, bucket, prefix)
		if err != nil {
			return 0, err
		}
		totalSize += size
	}
	g.Log().Debugf(u.ctx, "Upload total size is %d", totalSize)
	return totalSize, nil
}

func (u *uploader) uploadPath(localPath, filePath, bucket, prefix string) (int64, error) {
	defer utility.TimeCost()(u.ctx)
	rel, err := filepath.Rel(localPath, filePath)
	if err != nil {
		g.Log().Fatal(u.ctx, "Unable to get relative path:", filePath, err)
	}
	file, err := os.Open(filePath)
	if err != nil {
		g.Log().Debug(u.ctx, "Failed opening file", filePath, err)
		return 0, err
	}
	defer func() {
		_ = file.Close()
	}()
	info, err := file.Stat()
	if err != nil {
		return 0, err
	}
	input := &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(filepath.Join(prefix, rel)),
		Body:   file,
	}
	contentType, err := utility.GetFileContentType(filePath)
	if err == nil && contentType != "" {
		input.ContentType = aws.String(contentType)
	}
	result, err := u.up.Upload(context.TODO(), input)
	if err != nil {
		return 0, gerror.Wrap(err, "upload failed")
	}
	size := info.Size()
	g.Log().Debug(u.ctx, "Uploaded", filePath, size, result.Location)
	return size, nil
}

type fileWalk chan string

func (f fileWalk) Walk(path string, info os.FileInfo, err error) error {
	if err != nil {
		return err
	}
	if !info.IsDir() {
		f <- path
	}
	return nil
}
