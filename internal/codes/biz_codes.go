package codes

import (
	"net/http"

	"github.com/gogf/gf/v2/errors/gcode"
)

// http status, bisiness code, message.
var (
	CodeOK = New(http.StatusOK, gcode.CodeOK.Code(), "OK", gcode.CodeOK.Message())

	CodeAuthFailed       = New(http.StatusUnauthorized, 40101, "AuthFailed", "")
	CodeApiAuthFailed    = New(http.StatusUnauthorized, 40201, "ApiAuthFailed", "API 认证失败")
	CodeNotAuthorized    = New(http.StatusForbidden, 40301, "NotAuthorized", "")
	CodeNotFound         = New(http.StatusNotFound, 40401, "NotFound", "资源不存在")
	CodeValidationFailed = New(http.StatusBadRequest, 40001, "ValidationFailed", "验证失败")
	CodeNotAvailable     = New(http.StatusBadRequest, 40002, "NotAvailable", "不可用")
	CodeInvalidParam     = New(http.StatusBadRequest, 40003, "InvalidParam", "无效的参数")
	CodeDbErr            = New(http.StatusBadRequest, 40004, "DBError", "数据库错误")
	CodeBusinessErr      = New(http.StatusBadRequest, 40004, "CodeBusinessErr", "业务逻辑错误")

	CodeInternal = New(http.StatusInternalServerError, 50001, "InternalError", "出现内部错误")

	// oss
	CodeOssUploadFailed              = New(http.StatusInternalServerError, 50010, "OssUploadError", "对象存储上传失败")
	CodeOssDownloadFailed            = New(http.StatusInternalServerError, 50011, "OssDownloadError", "对象存储下载失败")
	CodeOssPreSignedUploadFailed     = New(http.StatusInternalServerError, 50012, "OssPreSignedUploadError", "对象存储上传预校验失败")
	CodeOssPreSignedDownloadFailed   = New(http.StatusInternalServerError, 50013, "OssPreSignedDownloadError", "对象存储下载预校验失败")
	CodeOssPreSignedGetContentFailed = New(http.StatusInternalServerError, 50014, "OssPreSignedGetContentError", "对象存储获取内容预校验失败")
	CodeOssDeleteFailed              = New(http.StatusInternalServerError, 50015, "OssDeleteError", "对象存储删除失败")

	// meta
	CodeMetaSaveFailed    = New(http.StatusInternalServerError, 50020, "MetaSaveError", "数据集保存失败")
	CodeMetaNameExist     = New(http.StatusInternalServerError, 50021, "MetaNameExist", "数据集名称已存在")
	CodeMetaEmptyName     = New(http.StatusInternalServerError, 50022, "MetaEmptyName", "数据集名称为空")
	CodeMetaBindingFailed = New(http.StatusInternalServerError, 50023, "MetaBindingFailed", "数据集绑定数据源失败")
	CodeMetaUpdateFailed  = New(http.StatusInternalServerError, 50024, "MetaUpdateError", "数据集更新失败")
	CodeMetaDeleteFailed  = New(http.StatusInternalServerError, 50025, "MetaDeleteError", "数据集删除失败")

	// user
	CodeUserLoginFailed                  = New(http.StatusInternalServerError, 50030, "UserLoginError", "用户登录失败")
	CodeUserCreateFailed                 = New(http.StatusInternalServerError, 50031, "UserCreateError", "创建用户失败")
	CodeUserDeleteFailed                 = New(http.StatusInternalServerError, 50032, "UserDeleteError", "删除用户失败")
	CodeUserUpdateFailed                 = New(http.StatusInternalServerError, 50033, "UserUpdateError", "更新用户失败")
	CodeUserListFailed                   = New(http.StatusInternalServerError, 50034, "UserListError", "获取用户列表失败")
	CodeUserBindRoleFailed               = New(http.StatusInternalServerError, 50035, "UserBindRoleError", "绑定角色失败")
	CodeUserCreateRoleFailed             = New(http.StatusInternalServerError, 50036, "UserCreateRoleError", "创建角色失败")
	CodeUserUpdateRoleFailed             = New(http.StatusInternalServerError, 50037, "UserUpdateRoleError", "更新角色失败")
	CodeUserDeleteRoleFailed             = New(http.StatusInternalServerError, 50038, "UserDeleteRoleError", "删除角色失败")
	CodeUserListRoleFailed               = New(http.StatusInternalServerError, 50039, "UserListRoleError", "获取角色列表失败")
	CodeUserListRolePermissionTreeFailed = New(http.StatusInternalServerError, 50130, "UserListRolePermissionTreeError", "获取权限列表失败")

	// view resource mark
	CodeResourceDeleteDenied = New(http.StatusInternalServerError, 50040, "ResourceDeleteDenied", "resource delete denied, only support system account")

	// user store
	CodeUserStoreDeleteFailed    = New(http.StatusInternalServerError, 50050, "UserStoreDeleteError", "数据源删除失败")
	CodeUserStoreDeleteObjFailed = New(http.StatusInternalServerError, 50051, "UserStoreDeleteObjError", "数据源删除对象失败")
	CodeUserStoreNameExist       = New(http.StatusInternalServerError, 50052, "UserStoreNameExist", "数据源名称已存在")
	CodeUserStoreCreateFailed    = New(http.StatusInternalServerError, 50053, "UserStoreCreateError", "数据源创建失败")

	// project
	CodeProjectDeleteFailed           = New(http.StatusInternalServerError, 50061, "ProjectDeleteError", "删除项目失败")
	CodeProjectCreateFailed           = New(http.StatusInternalServerError, 50062, "ProjectCreateError", "创建项目失败")
	CodeProjectUpdateFailed           = New(http.StatusInternalServerError, 50063, "ProjectUpdateError", "更新项目失败")
	CodeProjectCreateObjectFailed     = New(http.StatusInternalServerError, 50064, "ProjectCreateObjectError", "创建对象失败")
	CodeProjectDeleteObjectFailed     = New(http.StatusInternalServerError, 50065, "ProjectDeleteObjectError", "删除对象失败")
	CodeProjectUpdateObjectFailed     = New(http.StatusInternalServerError, 50066, "ProjectUpdateObjectError", "更新对象失败")
	CodeProjectObjectDuplicateFailed  = New(http.StatusInternalServerError, 50067, "ProjectObjectDuplicateError", "重名对象已存在")
	CodeProjectObjectAnnotatingFailed = New(http.StatusInternalServerError, 50068, "ProjectObjectAnnotatingError", "标注失败")
	CodeProjectObjectVerifyingFailed  = New(http.StatusInternalServerError, 50069, "ProjectObjectVerifyingError", "校验失败")

	// cron
	CodeCronUpdateProjectFailed = New(http.StatusInternalServerError, 50070, "CronUpdateProjectFailed", "定时任务更新项目信息失败")

	// episode
	CodeGetStoreInfoFailed  = New(http.StatusInternalServerError, 50080, "AssignmentQueryEmpty", "获取数据落盘信息失败")
	CodeCreateEpisodeFailed = New(http.StatusInternalServerError, 50081, "CreateEpisodeFailed", "创建数采记录失败")
	CodeListEpisodeFailed   = New(http.StatusInternalServerError, 50082, "ListEpisodeFailed", "查询采集记录失败")
	CodeUpdateEpisodeFailed = New(http.StatusInternalServerError, 50083, "UpdateEpisodeFailed", "更改采集记录失败")
	CodeDeleteEpisodeFailed = New(http.StatusInternalServerError, 50084, "DeleteEpisodeFailed", "修改采集记录失败")
	CodeGetEpisodeFailed    = New(http.StatusInternalServerError, 50085, "GetEpisodeFailed", "全局搜索episode信息失败")

	// task
	CodeCollectCreateTaskFailed     = New(http.StatusInternalServerError, 50090, "CreateTaskFailed", "创建任务失败")
	CodeCollectListTaskFailed       = New(http.StatusInternalServerError, 50091, "ListTaskFailed", "获取任务列表失败")
	CodeCollectAddTaskUserFailed    = New(http.StatusInternalServerError, 50092, "AddTaskUserFailed", "添加任务用户失败")
	CodeCollectUpdateTaskUserFailed = New(http.StatusInternalServerError, 50093, "UpdateTaskUserFailed", "更新任务用户失败")
	CodeCollectListTaskUserFailed   = New(http.StatusInternalServerError, 50094, "ListTaskUserFailed", "获取任务用户列表失败")
	CodeCollectUpdateTaskFailed     = New(http.StatusInternalServerError, 50095, "UpdateTaskFailed", "更新任务失败")
	CodeCollectDeleteTaskFailed     = New(http.StatusInternalServerError, 50096, "DeleteTaskFailed", "删除任务失败")
	CodeGetTaskDetailFailed         = New(http.StatusInternalServerError, 50097, "GetTaskDetailFailed", "获取任务详情失败")
	CodeCopyTaskFailed              = New(http.StatusInternalServerError, 50098, "CopyTaskFailed", "复制任务失败")

	// assignment
	CodeCreateAssignmentFailed  = New(http.StatusInternalServerError, 50100, "CreateAssignmentFailed", "创建委派记录失败")
	CodeListAssignmentFailed    = New(http.StatusInternalServerError, 50101, "ListAssignmentFailed", "获取委派记录失败")
	CodeGetAssignmentStatFailed = New(http.StatusInternalServerError, 50102, "GetAssignmentStatFailed", "获取单一动作采集员统计失败")
	CodeUpdateAssignmentFailed  = New(http.StatusInternalServerError, 50103, "UpdateAssignmentFailed", "更新委派记录失败")

	// task_sense
	CodeAddTaskSceneFailed    = New(http.StatusInternalServerError, 50110, "AddTaskSceneFailed", "创建场景失败")
	CodeDeleteTaskSceneFailed = New(http.StatusInternalServerError, 50111, "DeleteTaskSceneFailed", "删除场景失败")
	CodeUpdateTaskSceneFailed = New(http.StatusInternalServerError, 50112, "UpdateTaskSceneFailed", "更新场景失败")
	CodeGetTaskSceneFailed    = New(http.StatusInternalServerError, 50113, "GetTaskSceneFailed", "获取场景详情失败")
	CodeListTaskSceneFailed   = New(http.StatusInternalServerError, 50114, "ListTaskSceneFailed", "查询场景列表失败")

	// task_resource
	CodeAddTaskResourceFailed    = New(http.StatusInternalServerError, 50120, "AddTaskResourceFailed", "创建物体失败")
	CodeDeleteTaskResourceFailed = New(http.StatusInternalServerError, 50121, "DeleteTaskResourceFailed", "删除物体失败")
	CodeUpdateTaskResourceFailed = New(http.StatusInternalServerError, 50122, "UpdateTaskResourceFailed", "更新物体失败")
	CodeGetTaskResourceFailed    = New(http.StatusInternalServerError, 50123, "GetTaskResourceFailed", "获取物体详情失败")
	CodeListTaskResourceFailed   = New(http.StatusInternalServerError, 50124, "ListTaskResourceFailed", "查询物体列表失败")

	// info
	CodeGetInfoTemplateFailed  = New(http.StatusInternalServerError, 50130, "GetInfoTemplateFailed", "获取模板数据失败")
	CodeSaveInfoTemplateFailed = New(http.StatusInternalServerError, 50131, "SaveInfoTemplateFailed", "保存模板数据失败")

	// Lark
	CodeGetLarkDocTicketFailed = New(http.StatusInternalServerError, 50140, "GetLarkDocTicketFailed", "获取飞书文档助手凭证失败")

	// job
	CodeListTaskJobFailed        = New(http.StatusInternalServerError, 50200, "ListTaskJobFailed", "查询动作列表失败")
	CodeImportTaskJobFailed      = New(http.StatusInternalServerError, 50201, "ImportTaskJobFailed", "导入动作列表失败")
	CodeCreateTaskJobFailed      = New(http.StatusInternalServerError, 50203, "CreateTaskJobFailed", "新建动作失败")
	CodeDeleteTaskJobFailed      = New(http.StatusInternalServerError, 50202, "DeleteTaskJobFailed", "删除动作失败")
	CodeUpdateTaskJobFailed      = New(http.StatusInternalServerError, 50203, "UpdateTaskJobFailed", "更新动作失败")
	CodeCloneTaskJobFailed       = New(http.StatusInternalServerError, 50204, "CloneTaskJobFailed", "克隆动作失败")
	CodeGetJobDetailFailed       = New(http.StatusInternalServerError, 50205, "GetJobDetailFailed", "获取动作详情失败")
	CodeBuildTaskJobFailed       = New(http.StatusInternalServerError, 50206, "BuildTaskJobFailed", "创建实例任务失败")
	CodeSpecialFrameVisualFailed = New(http.StatusInternalServerError, 50207, "SpecialFrameVisualFailed", "获取帧可视化信息失败")

	// device
	CodeAddDeviceFailed    = New(http.StatusInternalServerError, 50300, "AddDeviceFailed", "创建设备记录失败")
	CodeDeleteDeviceFailed = New(http.StatusInternalServerError, 50301, "DeleteDeviceFailed", "删除设备记录失败")
	CodeUpdateDeviceFailed = New(http.StatusInternalServerError, 50302, "UpdateDeviceFailed", "更新设备记录失败")
	CodeListDeviceFailed   = New(http.StatusInternalServerError, 50303, "ListDeviceFailed", "查询设备记录失败")
	CodeCacheDeviceFailed  = New(http.StatusInternalServerError, 50303, "CacheDeviceFailed", "缓存设备记录失败")
	CodeSyncDeviceFailed   = New(http.StatusInternalServerError, 50303, "SyncDeviceFailed", "同步设备记录失败")

	// device_type
	CodeAddDeviceTypeFailed    = New(http.StatusInternalServerError, 50310, "AddDeviceTypeFailed", "创建设备类型失败")
	CodeDeleteDeviceTypeFailed = New(http.StatusInternalServerError, 50311, "DeleteDeviceTypeFailed", "删除设备类型失败")
	CodeUpdateDeviceTypeFailed = New(http.StatusInternalServerError, 50312, "UpdateDeviceTypeFailed", "更新设备类型失败")
	CodeListDeviceTypeFailed   = New(http.StatusInternalServerError, 50313, "ListDeviceTypeFailed", "查询设备类型失败")

	// review
	CodeGetReviewFailed           = New(http.StatusInternalServerError, 50400, "GetReviewFailed", "查询审核数据失败")
	CodeAddReviewFailed           = New(http.StatusInternalServerError, 50401, "AddReviewFailed", "提交审核记录失败")
	CodeGetReviewLabelFailed      = New(http.StatusInternalServerError, 50402, "GetReviewLabelFailed", "查询审核标注失败")
	CodeGetNextReviewFailed       = New(http.StatusInternalServerError, 50403, "GetNextReviewFailed", "获取下一条数据失败")
	CodeGetGetDeliverDetailFailed = New(http.StatusInternalServerError, 50404, "CodeGetGetDeliverDetailFailed", "获取一键交付详情失败")
	CodeOneClickDeliverFailed     = New(http.StatusInternalServerError, 50405, "CodeOneClickDeliverFailed", "一键交付失败")
	CodeGetNextEpisodeFailed      = New(http.StatusInternalServerError, 50406, "CodeGetNextEpisodeFailed", "获取下一条数据失败")
	CodeGetLastEpisodeFailed      = New(http.StatusInternalServerError, 50407, "CodeGetLastEpisodeFailed", "已经是最后一条数据了")

	// stat
	CodeGetStatInfoFailed        = New(http.StatusInternalServerError, 50500, "GetStatInfoFailed", "查询数据概览信息失败")
	CodeGetStatDateFailed        = New(http.StatusInternalServerError, 50501, "GetStatDateFailed", "获取时间统计失败")
	CodeGetStatUserFailed        = New(http.StatusInternalServerError, 50502, "GetStatUserFailed", "获取用户统计失败")
	CodeGetStatTaskCollectFailed = New(http.StatusInternalServerError, 50503, "GetStatTaskCollectFailed", "获取任务采集进度统计失败")
	CodeGetStatTaskCheckFailed   = New(http.StatusInternalServerError, 50504, "GetStatTaskCheckFailed", "获取任务审核进度统计失败")

	// download
	CodeDownloadReviewPassDataFailed    = New(http.StatusInternalServerError, 50601, "DownloadReviewPassDataFailed", "下载审核通过数据失败")
	CodeDownloadValidDurationDataFailed = New(http.StatusInternalServerError, 50602, "DownloadValidDurationDataFailed", "下载有效时长数据失败")

	// user_config
	CodeGetUserConfigFailed    = New(http.StatusInternalServerError, 50700, "GetUserConfigFailed", "查询用户配置失败")
	CodeDeleteUserConfigFailed = New(http.StatusInternalServerError, 50701, "DeleteUserConfigFailed", "删除用户配置失败")
	CodeUpdateUserConfigFailed = New(http.StatusInternalServerError, 50702, "UpdateUserConfigFailed", "更新用户配置失败")

	// tag
	CodeAddTagFailed    = New(http.StatusInternalServerError, 50800, "AddTagFailed", "增加标签失败")
	CodeListTagFailed   = New(http.StatusInternalServerError, 50801, "ListTagFailed", "查询标签失败")
	CodeDeleteTagFailed = New(http.StatusInternalServerError, 50802, "DeleteTagFailed", "删除标签失败")
	CodeUpdateTagFailed = New(http.StatusInternalServerError, 50803, "UpdateTagFailed", "更新标签失败")

	// release_note
	CodeAddReleaseNoteFailed       = New(http.StatusInternalServerError, 50900, "AddReleaseNoteFailed", "增加发版记录失败")
	CodeGetLatestReleaseNoteFailed = New(http.StatusInternalServerError, 50901, "GetLatestReleaseNoteFailed", "查询最近的一条发版记录失败")

	// user_group
	CodeAddUserGroupFailed    = New(http.StatusInternalServerError, 51000, "AddUserGroupFailed", "增加用户组失败")
	CodeListUserGroupFailed   = New(http.StatusInternalServerError, 51001, "ListUserGroupFailed", "查询用户组失败")
	CodeDeleteUserGroupFailed = New(http.StatusInternalServerError, 51002, "DeleteUserGroupFailed", "删除用户组失败")
	CodeUpdateUserGroupFailed = New(http.StatusInternalServerError, 51003, "UpdateUserGroupFailed", "更新用户组失败")

	// dataset
	CodeAddDatesetFailed       = New(http.StatusInternalServerError, 51100, "AddDatasetFailed", "增加数据集失败")
	CodeListDatesetFailed      = New(http.StatusInternalServerError, 51101, "ListDatasetFailed", "查询数据集失败")
	CodeDeleteDatesetFailed    = New(http.StatusInternalServerError, 51102, "DeleteDatasetFailed", "删除数据集失败")
	CodeUpdateDatasetFailed    = New(http.StatusInternalServerError, 51103, "UpdateDatasetFailed", "更新数据集失败")
	CodeDownloadDatasetFailed  = New(http.StatusInternalServerError, 51104, "DownloadDatasetFailed", "下载数据集失败")
	CodeRefreshDatasetFailed   = New(http.StatusInternalServerError, 51105, "RefreshDatasetFailed", "刷新数据集失败")
	CodeGetDatasetDetailFailed = New(http.StatusInternalServerError, 51106, "GetDatasetDetailFailed", "获取数据集详情失败")
	CodeGetDatasetMetaFailed   = New(http.StatusInternalServerError, 51107, "CodeGetDatasetMetaFailed", "获取数据集元数据失败")
	CodeNoDatasetNewMetaGen    = New(http.StatusInternalServerError, 51108, "CodeNoDatasetNewMetaGen", "没有新的采集数据产生")

	CodeAddDatesetVersionFailed    = New(http.StatusInternalServerError, 51200, "AddDatasetVersionFailed", "增加数据集版本失败")
	CodeUpdateDatesetVersionFailed = New(http.StatusInternalServerError, 51201, "UpdateDatasetVersionFailed", "更新数据集版本状态失败")
)
