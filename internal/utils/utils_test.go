package utils

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIf(t *testing.T) {
	assert := assert.New(t)

	// Test with true condition
	result1 := If(true, "hello", "world")
	assert.Equal("hello", result1)

	// Test with false condition
	result2 := If(false, "hello", "world")
	assert.Equal("world", result2)

	// Test with int type
	result3 := If(5 > 3, 100, 200)
	assert.Equal(100, result3)

	// Test with bool type
	result4 := If(1 == 1, true, false)
	assert.True(result4)
}

func TestDefault(t *testing.T) {
	assert := assert.New(t)

	// Test with nil input
	defaultValue := "default"
	result1 := Default(nil, &defaultValue)
	assert.Equal(&defaultValue, result1)
	assert.Equal("default", *result1)

	// Test with non-nil input
	inputValue := "input"
	result2 := Default(&inputValue, &defaultValue)
	assert.Equal(&inputValue, result2)
	assert.Equal("input", *result2)

	// Test with int type
	defaultInt := 42
	inputInt := 10
	result3 := Default(&inputInt, &defaultInt)
	assert.Equal(&inputInt, result3)
	assert.Equal(10, *result3)

	result4 := Default[int](nil, &defaultInt)
	assert.Equal(&defaultInt, result4)
	assert.Equal(42, *result4)
}

func TestMap(t *testing.T) {
	assert := assert.New(t)

	// Test string to int mapping
	strings := []string{"1", "2", "3", "4"}
	ints := Map(strings, func(s string) int {
		i, _ := strconv.Atoi(s)
		return i
	})
	assert.Equal([]int{1, 2, 3, 4}, ints)

	// Test int to string mapping
	numbers := []int{1, 2, 3}
	strs := Map(numbers, func(i int) string {
		return strconv.Itoa(i * 2)
	})
	assert.Equal([]string{"2", "4", "6"}, strs)

	// Test empty slice
	empty := []string{}
	emptyResult := Map(empty, func(s string) int {
		return 0
	})
	assert.Empty(emptyResult)
	assert.Equal([]int{}, emptyResult)
}

func TestDiff(t *testing.T) {
	assert := assert.New(t)

	// Test normal case
	oldArr := []int{1, 2, 3, 4}
	newArr := []int{3, 4, 5, 6}
	added, removed := Diff(oldArr, newArr)
	assert.ElementsMatch([]int{5, 6}, added)
	assert.ElementsMatch([]int{1, 2}, removed)

	// Test no differences
	same1 := []string{"a", "b", "c"}
	same2 := []string{"a", "b", "c"}
	added2, removed2 := Diff(same1, same2)
	assert.Empty(added2)
	assert.Empty(removed2)

	// Test completely different
	old := []int{1, 2}
	new := []int{3, 4}
	added3, removed3 := Diff(old, new)
	assert.ElementsMatch([]int{3, 4}, added3)
	assert.ElementsMatch([]int{1, 2}, removed3)

	// Test empty slices
	empty1 := []int{}
	empty2 := []int{}
	added4, removed4 := Diff(empty1, empty2)
	assert.Empty(added4)
	assert.Empty(removed4)

	// Test one empty
	nonEmpty := []int{1, 2}
	empty := []int{}
	added5, removed5 := Diff(nonEmpty, empty)
	assert.Empty(added5)
	assert.ElementsMatch([]int{1, 2}, removed5)

	added6, removed6 := Diff(empty, nonEmpty)
	assert.ElementsMatch([]int{1, 2}, added6)
	assert.Empty(removed6)
}

type TestStruct struct {
	ID   int
	Name string
}

func TestDiffWithKey(t *testing.T) {
	assert := assert.New(t)

	// Test with struct and key function
	oldStructs := []TestStruct{
		{ID: 1, Name: "Alice"},
		{ID: 2, Name: "Bob"},
		{ID: 3, Name: "Charlie"},
	}
	newStructs := []TestStruct{
		{ID: 2, Name: "Bob Updated"}, // Updated but same ID
		{ID: 3, Name: "Charlie"},
		{ID: 4, Name: "David"}, // New
	}

	keyFunc := func(s TestStruct) int { return s.ID }
	added, removed := DiffWithKey(oldStructs, newStructs, keyFunc)

	assert.Len(added, 1)
	assert.Equal(4, added[0].ID)
	assert.Equal("David", added[0].Name)

	assert.Len(removed, 1)
	assert.Equal(1, removed[0].ID)
	assert.Equal("Alice", removed[0].Name)

	// Test with string key
	oldItems := []TestStruct{
		{ID: 1, Name: "Alice"},
		{ID: 2, Name: "Bob"},
	}
	newItems := []TestStruct{
		{ID: 3, Name: "Alice"}, // Same name, different ID
		{ID: 4, Name: "Charlie"},
	}

	nameKeyFunc := func(s TestStruct) string { return s.Name }
	added2, removed2 := DiffWithKey(oldItems, newItems, nameKeyFunc)

	assert.Len(added2, 1)
	assert.Equal("Charlie", added2[0].Name)

	assert.Len(removed2, 1)
	assert.Equal("Bob", removed2[0].Name)
}

func TestUniq(t *testing.T) {
	assert := assert.New(t)

	// Test with duplicates
	input := []int{1, 2, 2, 3, 3, 3, 4}
	result := Uniq(input)
	assert.ElementsMatch([]int{1, 2, 3, 4}, result)

	// Test order preservation
	input2 := []string{"a", "b", "a", "c", "b"}
	result2 := Uniq(input2)
	assert.Equal([]string{"a", "b", "c"}, result2)

	// Test no duplicates
	input3 := []int{1, 2, 3, 4}
	result3 := Uniq(input3)
	assert.Equal([]int{1, 2, 3, 4}, result3)

	// Test empty slice
	empty := []string{}
	emptyResult := Uniq(empty)
	assert.Empty(emptyResult)

	// Test single element
	single := []int{42}
	singleResult := Uniq(single)
	assert.Equal([]int{42}, singleResult)

	// Test all same elements
	allSame := []string{"a", "a", "a", "a"}
	allSameResult := Uniq(allSame)
	assert.Equal([]string{"a"}, allSameResult)
}

func TestUniqWithKey(t *testing.T) {
	assert := assert.New(t)

	// Test with struct and ID key function
	structs := []TestStruct{
		{ID: 1, Name: "Alice"},
		{ID: 2, Name: "Bob"},
		{ID: 1, Name: "Alice Duplicate"}, // Same ID, different name
		{ID: 3, Name: "Charlie"},
		{ID: 2, Name: "Bob Duplicate"}, // Same ID, different name
	}

	keyFunc := func(s TestStruct) int { return s.ID }
	result := UniqWithKey(structs, keyFunc)

	assert.Len(result, 3)
	assert.Equal(1, result[0].ID)
	assert.Equal("Alice", result[0].Name) // First occurrence kept
	assert.Equal(2, result[1].ID)
	assert.Equal("Bob", result[1].Name) // First occurrence kept
	assert.Equal(3, result[2].ID)
	assert.Equal("Charlie", result[2].Name)

	// Test with name key function
	nameKeyFunc := func(s TestStruct) string { return s.Name }
	structs2 := []TestStruct{
		{ID: 1, Name: "Alice"},
		{ID: 2, Name: "Bob"},
		{ID: 3, Name: "Alice"}, // Same name, different ID
	}
	result2 := UniqWithKey(structs2, nameKeyFunc)

	assert.Len(result2, 2)
	assert.Equal("Alice", result2[0].Name)
	assert.Equal(1, result2[0].ID) // First occurrence kept
	assert.Equal("Bob", result2[1].Name)

	// Test empty slice
	empty := []TestStruct{}
	emptyResult := UniqWithKey(empty, keyFunc)
	assert.Empty(emptyResult)
}
