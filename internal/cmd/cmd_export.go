package cmd

import (
	"aim-data/internal/consts"
	"aim-data/internal/dao"
	gmodel "aim-data/internal/dao/model"
	"aim-data/internal/model"
	"aim-data/internal/model/do"
	"aim-data/internal/model/entity"
	"aim-data/internal/pkg/gorm"
	"aim-data/internal/pkg/oss"
	"aim-data/internal/service"
	"aim-data/utility"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"strconv"
	"time"

	"github.com/gogf/gf/v2/os/gtime"
	"gopkg.in/yaml.v2"

	"github.com/gogf/gf/v2/util/gconv"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"

	"github.com/gogf/gf/v2/frame/g"
)

const (
	initial       = 0
	exporting     = 1
	exportSuccess = 2
	exportFailed  = -1
)

type cMainExportInput struct {
	g.Meta `name:"export" brief:"导出指定 TaskID 的所有数据" usage:"export -t <task_id>"`
	SyncID uint `name:"sync_id" short:"s" brief:"对应导出任务ID" usage:"" example:"1024" json:"sync_id"`
	//OssConfig string `name:"oss_config" short:"c" brief:"OSS 配置文件路径（用于存储导出结果）" usage:"" example:"./oss_config.yaml"`
}

type Req struct {
	TaskID uint `json:"task_id"`
}

type OssConfig struct {
	Endpoint      string `json:"endpoint"`
	AK            string `json:"ak"`
	SK            string `json:"sk"`
	DefaultBucket string `json:"defaultBucket" yaml:"defaultBucket"`
	UsePathStyle  bool   `json:"usePathStyle" yaml:"usePathStyle"`
}

type cMainExportOutput struct {
}

func (c *cMain) Export(ctx context.Context, in cMainExportInput) (out *cMainExportOutput, err error) {
	g.Log().Infof(ctx, "syncID:%d start export", in.SyncID)
	err = gorm.MigrateTargetTabel(&gmodel.DataioAsyncTask{})
	if err != nil {
		return nil, err
	}
	var record *entity.DataioAsyncTask
	err = dao.DataioAsyncTask.Ctx(ctx).
		Where(dao.DataioAsyncTask.Columns().Id, in.SyncID).
		Where(dao.DataioAsyncTask.Columns().Status, initial).
		Scan(&record)
	if err != nil {
		return nil, err
	}
	if record == nil {
		return nil, gerror.New("record not found")
	}

	result, err := dao.DataioAsyncTask.Ctx(ctx).
		Where(dao.DataioAsyncTask.Columns().Id, record.Id).
		Update(do.DataioAsyncTask{Status: exporting, UpdatedAt: gtime.Now()})
	if err != nil {
		return nil, err
	}
	if rowsAffectedCnt, _ := result.RowsAffected(); rowsAffectedCnt != 1 {
		g.Log().Infof(ctx, "syncID:%d is exporting", in.SyncID)
		return nil, nil
	}

	req := new(Req)
	err = json.Unmarshal([]byte(record.InputParams), req)
	if err != nil {
		return nil, err
	}

	res := new(model.SyncDataAsset)
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var task *model.SyncTask
		err = tx.Model(dao.Task.Table()).Where(dao.Task.Columns().Id, req.TaskID).Scan(&task)
		if err != nil {
			return gerror.New(err.Error())
		}
		if task == nil {
			return gerror.New("task not found")
		}
		res.Task = *task
		tenant, err := service.Tenant().GetTenant(ctx, strconv.Itoa(task.TenantID))
		if err != nil {
			return gerror.New(err.Error())
		}
		ctx = context.WithValue(ctx, consts.CtxAccessTenantConfigKey, tenant.Config)
		ctx = context.WithValue(ctx, consts.CtxAccessTenantIdKey, uint(tenant.Id))
		res.Meta.SourceConf = tenant.Config.String()

		var temp *model.SyncJobTemplate
		err = tx.Model(dao.JobTemplate.Table()).Where(dao.JobTemplate.Columns().TaskId, req.TaskID).Scan(&temp)
		if err != nil {
			return gerror.New(err.Error())
		}
		if temp == nil {
			return gerror.New("job template not found")
		}
		res.Task.JobTemplate = *temp

		var deviceType *model.SyncDeviceType
		err = tx.Model(dao.DeviceType.Table()).Where(dao.DeviceType.Columns().Id, task.DeviceTypeId).Scan(&deviceType)
		if err != nil {
			return gerror.New(err.Error())
		}
		if deviceType != nil {
			res.Task.DeviceType = deviceType
		}

		taskVariables := make([]model.SyncTaskVariable, 0)
		err = tx.Model(dao.TaskVariable.Table()).Where(dao.TaskVariable.Columns().TaskId, req.TaskID).Scan(&taskVariables)
		if err != nil {
			return gerror.New(err.Error())
		}
		res.Task.TaskVariables = taskVariables

		resources, err := getResourceFromVariables(ctx, taskVariables)
		if err != nil {
			return err
		}
		res.Task.TaskResources = resources

		// 获取任务和任务关联物体的标签信息
		relTags, err := getTaskRelTag(ctx, req.TaskID, resources)
		if err != nil {
			return err
		}
		res.Task.Tags = relTags

		jobs := make([]model.SyncJob, 0)
		err = tx.Model(dao.Job.Table()).Where(dao.Job.Columns().TaskId, req.TaskID).Scan(&jobs)
		if err != nil {
			return gerror.New(err.Error())
		}
		res.Task.Jobs = jobs

		episodes := make([]model.SyncEpisode, 0)
		err = tx.Model(dao.Episode.Table()).Where(dao.Episode.Columns().TaskId, req.TaskID).Scan(&episodes)
		if err != nil {
			return gerror.New(err.Error())
		}
		jobEpisodes := make(map[uint][]model.SyncEpisode)
		for _, episode := range episodes {
			jobEpisodes[episode.JobId] = append(jobEpisodes[episode.JobId], episode)
		}

		reviews := make([]model.SyncReview, 0)
		err = tx.Model(dao.Review.Table()).Where(dao.Review.Columns().TaskId, req.TaskID).Scan(&reviews)
		if err != nil {
			return gerror.New(err.Error())
		}
		episodeReview := make(map[uint64][]model.SyncReview)
		for _, review := range reviews {
			episodeReview[review.EpisodeId] = append(episodeReview[review.EpisodeId], review)
		}

		for jobIndex, job := range jobs {
			if _, ok := jobEpisodes[job.Id]; ok {
				jobs[jobIndex].Episodes = jobEpisodes[job.Id]
				for i, episode := range jobs[jobIndex].Episodes {
					if _, ok1 := episodeReview[episode.Id]; ok1 {
						jobs[jobIndex].Episodes[i].Reviews = episodeReview[episode.Id]
					} else {
						jobs[jobIndex].Episodes[i].Reviews = make([]model.SyncReview, 0)
					}
				}
			} else {
				job.Episodes = make([]model.SyncEpisode, 0)
			}
		}
		res.Task.Jobs = jobs
		return nil
	})
	toUpdate := do.DataioAsyncTask{
		Status: exportFailed,
	}
	if err != nil {
		g.Log().Warningf(ctx, "taskID:%d export failed,err:%+v", req.TaskID, err)
		dao.DataioAsyncTask.Ctx(ctx).Where(dao.DataioAsyncTask.Columns().Id, record.Id).
			Update(toUpdate)
		return nil, err
	}
	all, err := json.MarshalIndent(res, "", "	")
	if err != nil {
		g.Log().Warningf(ctx, "taskID:%d export failed,err:%+v", req.TaskID, err)
		return
	}
	toUpdate.Status = exportSuccess
	toUpdate.OutputResult = string(all)
	_, err = dao.DataioAsyncTask.Ctx(ctx).Where(dao.DataioAsyncTask.Columns().Id, record.Id).
		Update(toUpdate)
	if err != nil {
		g.Log().Warningf(ctx, "taskID:%d export failed,err:%+v", req.TaskID, err)
		return
	}
	g.Log().Infof(ctx, "taskID:%d export success", req.TaskID)
	err = saveResultToFile(fmt.Sprintf("result-%d.json", time.Now().Unix()), res)
	if err != nil {
		g.Log().Warningf(ctx, "taskID:%d export failed,err:%+v", req.TaskID, err)
	}
	return nil, err
}

// getTaskRelTag 获取任务相关的所有标签信息
// 包括任务标签、设备标签、任务资源标签以及父标签详情
func getTaskRelTag(ctx context.Context, taskID uint, taskResource []model.SyncTaskResource) (*model.RelTag, error) {
	// 提取任务资源ID列表
	taskResourceIDs := extractTaskResourceIDs(taskResource)

	// 用于收集所有父标签代码
	ptagCodes := make(map[string]uint)

	// 获取任务标签
	taskTags, taskPtagCodes, err := getTaskTags(ctx, taskID)
	if err != nil {
		return nil, err
	}
	// 合并任务标签的父标签代码
	for code, id := range taskPtagCodes {
		ptagCodes[code] = id
	}

	// 获取任务资源标签
	taskResourceTags, resourcePtagCodes, err := getTaskResourceTags(ctx, taskResourceIDs)
	if err != nil {
		return nil, err
	}
	// 合并任务资源标签的父标签代码
	for code, id := range resourcePtagCodes {
		ptagCodes[code] = id
	}

	// 获取设备类型标签
	deviceTags, devicePtagCodes, err := getDeviceTypeTags(ctx, taskID)
	if err != nil {
		return nil, err
	}
	// 合并设备类型标签的父标签代码
	for code, id := range devicePtagCodes {
		ptagCodes[code] = id
	}

	// 获取父标签详情
	parentTagDetail, err := utility.GetTagIdChain(ctx, ptagCodes)
	if err != nil {
		return nil, err
	}

	return &model.RelTag{
		DeviceTags:       deviceTags,
		TaskTags:         taskTags,
		TaskResourceTags: taskResourceTags,
		ParentTagDetail:  parentTagDetail,
	}, nil
}

// extractTaskResourceIDs 提取任务资源ID列表
func extractTaskResourceIDs(taskResource []model.SyncTaskResource) []uint {
	taskResourceIDs := make([]uint, 0, len(taskResource))
	for _, v := range taskResource {
		taskResourceIDs = append(taskResourceIDs, v.Id)
	}
	return taskResourceIDs
}

// getTaskTags 获取任务关联的标签，包括动作步骤中的原子能力
func getTaskTags(ctx context.Context, taskID uint) (map[string][]entity.Tag, map[string]uint, error) {
	// 查询任务关联的标签ID
	resMap, err := service.Tag().QueryResourceTagByResType(ctx, []uint{taskID}, dao.Task.Table())
	if err != nil {
		return nil, nil, gerror.New(err.Error())
	}

	// 收集所有标签ID
	tagIDs := make([]uint, 0)
	for _, uints := range resMap {
		tagIDs = append(tagIDs, uints...)
	}

	if len(tagIDs) == 0 {
		return make(map[string][]entity.Tag), make(map[string]uint), nil
	}

	// 获取标签详情
	tagsDetail, err := service.Tag().QueryTagDetailByIDs(ctx, tagIDs)
	if err != nil {
		return nil, nil, gerror.New(err.Error())
	}

	// 获取父标签详情
	pTagIDs := make([]uint, 0, len(tagsDetail))
	for _, item := range tagsDetail {
		pTagIDs = append(pTagIDs, uint(item.Pid))
	}

	pTagDetails, err := service.Tag().QueryTagDetailByIDs(ctx, pTagIDs)
	if err != nil {
		return nil, nil, gerror.New(err.Error())
	}

	// 构建父标签代码映射
	ptagCodes := make(map[string]uint)
	for _, tag := range pTagDetails {
		ptagCodes[tag.Code] = tag.Id
	}

	// 按父标签分组
	taskTags := make(map[string][]entity.Tag)
	for _, tag := range tagsDetail {
		parentCode := pTagDetails[uint(tag.Pid)].Code
		taskTags[parentCode] = append(taskTags[parentCode], *tag)
	}

	return taskTags, ptagCodes, nil
}

// getTaskResourceTags 获取任务资源标签
func getTaskResourceTags(ctx context.Context, taskResourceIDs []uint) (map[string][]entity.Tag, map[string]uint, error) {
	if len(taskResourceIDs) == 0 {
		return make(map[string][]entity.Tag), make(map[string]uint), nil
	}

	// 查询任务资源关联的标签ID
	resMap, err := service.Tag().QueryResourceTagByResType(ctx, taskResourceIDs, dao.TaskResource.Table())
	if err != nil {
		return nil, nil, gerror.New(err.Error())
	}

	// 收集所有标签ID
	taskResourceTagIDs := make([]uint, 0)
	for _, uints := range resMap {
		taskResourceTagIDs = append(taskResourceTagIDs, uints...)
	}

	if len(taskResourceTagIDs) == 0 {
		return make(map[string][]entity.Tag), make(map[string]uint), nil
	}

	// 获取标签详情
	tagsDetail, err := service.Tag().QueryTagDetailByIDs(ctx, taskResourceTagIDs)
	if err != nil {
		return nil, nil, gerror.New(err.Error())
	}

	// 获取父标签详情
	pTagIDs := make([]uint, 0, len(tagsDetail))
	for _, item := range tagsDetail {
		pTagIDs = append(pTagIDs, uint(item.Pid))
	}

	pTagDetails, err := service.Tag().QueryTagDetailByIDs(ctx, pTagIDs)
	if err != nil {
		return nil, nil, gerror.New(err.Error())
	}

	// 构建父标签代码映射
	ptagCodes := make(map[string]uint)
	for _, tag := range pTagDetails {
		ptagCodes[tag.Code] = tag.Id
	}

	// 按父标签分组
	taskResourceTags := make(map[string][]entity.Tag)
	for _, tag := range tagsDetail {
		parentCode := pTagDetails[uint(tag.Pid)].Code
		taskResourceTags[parentCode] = append(taskResourceTags[parentCode], *tag)
	}

	return taskResourceTags, ptagCodes, nil
}

// getDeviceTypeTags 获取设备类型标签
func getDeviceTypeTags(ctx context.Context, taskID uint) (map[string][]entity.Tag, map[string]uint, error) {
	deviceTags := make(map[string][]entity.Tag)
	ptagCodes := make(map[string]uint)

	// 获取任务的设备类型ID
	deviceTypeVal, err := dao.Task.Ctx(ctx).Fields(dao.Task.Columns().DeviceTypeId).Where(dao.Task.Columns().Id, taskID).Value()
	if err != nil {
		return nil, nil, gerror.New(err.Error())
	}

	// 如果没有设备类型，返回空映射
	if deviceTypeVal.Uint() == 0 {
		return deviceTags, ptagCodes, nil
	}

	// 获取设备类型标签
	deviceTypeTags, err := service.Device().GetDeviceTypeTags(ctx, deviceTypeVal.Uint())
	if err != nil {
		// 如果获取设备类型标签失败，记录日志但不返回错误
		g.Log().Warningf(ctx, "获取设备类型标签失败, deviceTypeId: %d, err: %v", deviceTypeVal.Uint(), err)
		return deviceTags, ptagCodes, nil
	}

	// 处理设备类型标签
	for k, entityTags := range deviceTypeTags {
		deviceTags[k] = entityTags

		// 获取父标签信息
		parentTag, err := service.Tag().QueryTagFromCode(ctx, k)
		if err != nil {
			return nil, nil, gerror.New(err.Error())
		}
		ptagCodes[parentTag.Code] = parentTag.Id
	}

	return deviceTags, ptagCodes, nil
}

func getResourceFromVariables(ctx context.Context, variables []model.SyncTaskVariable) ([]model.SyncTaskResource, error) {
	resourceIDs := make([]int, 0, len(variables))
	for _, v := range variables {
		if v.Type == 1 {
			ids := gconv.Ints(v.Values)
			resourceIDs = append(resourceIDs, ids...)
		}
	}
	resources := make([]model.SyncTaskResource, 0, len(resourceIDs))
	err := dao.TaskResource.Ctx(ctx).WhereIn(dao.TaskResource.Columns().Id, resourceIDs).Scan(&resources)
	if err != nil {
		return nil, gerror.New(err.Error())
	}
	for i, resource := range resources {
		array, err := dao.TagRel.Ctx(ctx).Where(dao.TagRel.Columns().ResourceId, resource.Id).
			Where(dao.TagRel.Columns().ResourceType, dao.TaskResource.Table()).Array(dao.TagRel.Columns().TagId)
		if err != nil {
			return nil, gerror.New(err.Error())
		}
		resources[i].TagIDs = gconv.Ints(array)
	}
	return resources, nil
}

func saveResultToFile(filename string, stats *model.SyncDataAsset) error {
	f, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer f.Close()
	all, _ := json.MarshalIndent(stats, "", "	")
	f.Write(all)
	return nil
}

func getOssConfigFromYaml(ctx context.Context, path string) (*OssConfig, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		g.Log().Errorf(ctx, "read file %s error: %v", path, err)
		return nil, err
	}

	// 定义一个 map 用于解析 oss 字段
	var cfg struct {
		Oss OssConfig `yaml:"oss"`
	}

	if err := yaml.Unmarshal(data, &cfg); err != nil {
		g.Log().Errorf(ctx, "unmarshal oss yaml file %s error: %v", path, err)
		return nil, err
	}
	return &cfg.Oss, nil
}

func saveResult(ctx context.Context, ossConfig *OssConfig, batchTimeStr, filename string, stats *model.SyncDataAsset) error {
	f, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer f.Close()
	all, _ := json.MarshalIndent(stats, "", "	")
	f.Write(all)

	path := fmt.Sprintf("s3://%s/task-export/%s/%s", ossConfig.DefaultBucket, batchTimeStr, filename)
	err = oss.Svc(ctx,
		oss.WithEndpoint(ossConfig.Endpoint),
		oss.WithCredentials(ossConfig.AK, ossConfig.SK),
		oss.WithUsePathStyle(ossConfig.UsePathStyle),
	).UploadData(io.NopCloser(bytes.NewReader(all)), path, oss.UploadWithMime("text/plain"),
		oss.UploadWithContentLength(int64(len(all))))
	if err != nil {
		return gerror.New(err.Error())
	}
	g.Log().Infof(ctx, "upload file:%s succeed", filename)
	return nil
}
