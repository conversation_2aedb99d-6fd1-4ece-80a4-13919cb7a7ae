package cmd

import (
	"aim-data/internal/dao/model"
	"aim-data/internal/pkg/gorm"
	"aim-data/internal/service"
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
)

type cMainDataIODaemonInput struct {
	g.Meta         `name:"dataio-daemon" brief:"数据IO守护进程" usage:"dataio-daemon" example:"dataio-daemon -w 5"`
	MaxWorkerCount int    `json:"maxWorkerCount" short:"w" default:"3"`
	ImageOverride  string `short:"i" name:"image-override" default:"" brief:"镜像覆盖 (默认不覆盖)"`
	Config         string `short:"c" name:"config" brief:"配置文件 (默认 config.yaml)"`
}

type cMainDataIODaemonOutput struct {
}

func loop(ctx context.Context, d time.Duration, f func(ctx context.Context) error) {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	sigChan := make(chan os.Signal, 1)
	defer close(sig<PERSON>han)
	signal.Notify(sigChan, syscall.SIGTERM, syscall.SIGINT)

	timer := time.NewTicker(d)
	defer timer.Stop()

	go func() {
		<-sigChan
		g.Log().Info(ctx, "captured ctrl+c, shutting down...")
		cancel()
	}()

	for {
		select {
		case <-ctx.Done():
			g.Log().Info(ctx, "shutdown complete")
			return
		case <-timer.C:
			if err := f(ctx); err != nil {
				g.Log().Error(ctx, err)
			}
		}
	}
}

func (c *cMain) DataIODaemon(ctx context.Context, in cMainDataIODaemonInput) (out *cMainDataIODaemonOutput, err error) {
	if in.Config != "" {
		//nolint: forcetypeassert
		g.Cfg().GetAdapter().(*gcfg.AdapterFile).SetFileName(in.Config)
	}

	err = gorm.MigrateTargetTabel(&model.DataioAsyncTask{})
	if err != nil {
		g.Log().Fatal(ctx, err)
	}

	k := 0

	loop(ctx, 5*time.Second, func(ctx context.Context) error {
		k = (k + 1) % 100
		if k == 0 {
			if err := service.DataIO().Cleanup(ctx); err != nil {
				g.Log().Warning(ctx, err)
			}
		}
		if n, err := service.DataIO().GetRunningJobCount(ctx); err != nil {
			return err
		} else if n >= in.MaxWorkerCount {
			g.Log().Infof(ctx, "running job count is greater than %d, waiting...", in.MaxWorkerCount)
			return nil
		}
		pendingTask, err := service.DataIO().GetPendingTask(ctx)
		if err != nil {
			return err
		}
		if pendingTask != nil {
			g.Log().Infof(ctx, "start %s task[%d]", pendingTask.Kind, pendingTask.Id)
			service.DataIO().ExecuteJob(ctx, pendingTask, in.ImageOverride)
		}
		return nil
	})

	return &cMainDataIODaemonOutput{}, err
}
