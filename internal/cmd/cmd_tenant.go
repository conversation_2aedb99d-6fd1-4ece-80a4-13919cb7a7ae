package cmd

import (
	"aidea-admin/internal/dao"
	"aidea-admin/internal/model/do"
	"aidea-admin/internal/model/entity"
	"aidea-admin/internal/service"
	"context"
	"os"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/jedib0t/go-pretty/v6/table"
)

type cMainTenantCreateInput struct {
	g.Meta    `name:"tenant-create" brief:"租户管理" usage:"tenant-create foo [OPTION]" example:"tenant-create foo --clone-from=agibot"`
	Name      string `arg:"true" name:"name" v:"required|length:3,12" brief:"租户名称"`
	CloneFrom string `short:"c" name:"clone-from" brief:"克隆租户"`
}

type cMainTenantCreateOutput struct {
	AdminPassword string `json:"admin_password"`
}

func (c *cMain) TenantCreate(ctx context.Context, in cMainTenantCreateInput) (out *cMainTenantCreateOutput, err error) {
	g.Log().Infof(ctx, "Create tenant: %s", in.Name)

	defaultConfig := g.Map{}
	if in.CloneFrom != "" {
		if t, err := dao.Tenant.GetByName(ctx, in.CloneFrom); err == nil {
			defaultConfig = t.Config.Map()
		} else {
			return nil, gerror.Newf("租户 %s 不存在", in.CloneFrom)
		}
	} else {
		if t, err := dao.Tenant.GetByID(ctx, 1); err == nil {
			defaultConfig = t.Config.Map()
		}
	}

	tenant, err := service.Tenant().Create(ctx, service.CasdoorClient(), &do.Tenant{
		Name:   in.Name,
		Config: gjson.New(defaultConfig),
	})
	if err != nil {
		return nil, err
	}

	adminPassword := tenant.Config.Get("adminPassword", "").String()
	g.Log().Infof(ctx, "Tenant %s created successfully, admin password: %s", tenant.Name, adminPassword)
	return &cMainTenantCreateOutput{
		AdminPassword: adminPassword,
	}, nil
}

type cMainTenantListInput struct {
	g.Meta `name:"tenant-list" brief:"租户管理" usage:"tenant-list [OPTION]" example:"tenant-list"`
}

type cMainTenantListOutput struct {
	Tenants []*entity.Tenant `json:"tenants"`
}

func (c *cMain) TenantList(ctx context.Context, in cMainTenantListInput) (out *cMainTenantListOutput, err error) {
	tenants, _, err := service.Tenant().GetAll(ctx)
	if err != nil {
		return nil, err
	}

	t := table.NewWriter()
	t.SetOutputMirror(os.Stdout)
	t.AppendHeader(table.Row{"ID", "Name", "Display_Name", "Created_At"})
	for _, tenant := range tenants {
		t.AppendRow(table.Row{tenant.Id, tenant.Name, tenant.DisplayName, tenant.CreatedAt.ISO8601()})
	}
	t.Render()

	return &cMainTenantListOutput{
		Tenants: tenants,
	}, nil
}
