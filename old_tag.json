[{"id": 651, "tenant_id": 3, "code": "task", "label": "任务标签", "pid": 0, "level": 1, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 652, "tenant_id": 3, "code": "task_stage", "label": "任务用途", "pid": 651, "level": 2, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 653, "tenant_id": 3, "code": "task_stage_prod", "label": "正式采集", "pid": 652, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 654, "tenant_id": 3, "code": "task_stage_test", "label": "开发测试", "pid": 652, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 655, "tenant_id": 3, "code": "task_stage_pilot_prod", "label": "运营试采", "pid": 652, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 656, "tenant_id": 3, "code": "task_stage_simulation", "label": "仿真评测", "pid": 652, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 657, "tenant_id": 3, "code": "task_scene_classification", "label": "场景分类", "pid": 651, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 658, "tenant_id": 3, "code": "task_scene_classification_工厂", "label": "工厂", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 659, "tenant_id": 3, "code": "task_scene_classification_家居", "label": "家居", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 660, "tenant_id": 3, "code": "task_scene_classification_超市", "label": "超市", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 661, "tenant_id": 3, "code": "task_scene_classification_咖啡厅", "label": "咖啡厅", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 662, "tenant_id": 3, "code": "task_scene_classification_餐厅", "label": "餐厅", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 663, "tenant_id": 3, "code": "task_scene_classification_厨房", "label": "厨房", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 664, "tenant_id": 3, "code": "task_scene_classification_奶茶店", "label": "奶茶店", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 665, "tenant_id": 3, "code": "task_scene_classification_浴室", "label": "浴室", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 666, "tenant_id": 3, "code": "task_scene_classification_客厅", "label": "客厅", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 667, "tenant_id": 3, "code": "task_scene_classification_烹饪", "label": "烹饪", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 668, "tenant_id": 3, "code": "task_scene_classification_Household", "label": "Household", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 669, "tenant_id": 3, "code": "task_scene_classification_会议室", "label": "会议室", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 670, "tenant_id": 3, "code": "task_scene_classification_办公区", "label": "办公区", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 671, "tenant_id": 3, "code": "task_scene_classification_办公", "label": "办公", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 672, "tenant_id": 3, "code": "task_scene_classification_卧室", "label": "卧室", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 673, "tenant_id": 3, "code": "task_scene_classification_工业", "label": "工业", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 674, "tenant_id": 3, "code": "task_scene_classification_商超", "label": "商超", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 675, "tenant_id": 3, "code": "task_scene_classification_办公室", "label": "办公室", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 676, "tenant_id": 3, "code": "task_scene_classification_餐饮", "label": "餐饮", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 677, "tenant_id": 3, "code": "task_scene_classification_书房", "label": "书房", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 678, "tenant_id": 3, "code": "task_scene_classification_其他", "label": "其他", "pid": 657, "level": 3, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 679, "tenant_id": 3, "code": "task_collect_ontology", "label": "采集本体", "pid": 651, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 680, "tenant_id": 3, "code": "task_end_kind", "label": "末端类型", "pid": 651, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 681, "tenant_id": 3, "code": "task_remote_control_kind", "label": "遥操类型", "pid": 651, "level": 2, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 682, "tenant_id": 3, "code": "task_remote_control_kind_vr", "label": "VR", "pid": 681, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 683, "tenant_id": 3, "code": "task_remote_control_kind_motion_capture", "label": "动捕", "pid": 681, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 684, "tenant_id": 3, "code": "task_collect_mode", "label": "采集模式", "pid": 651, "level": 2, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 685, "tenant_id": 3, "code": "task_collect_mode_full_body", "label": "全身", "pid": 684, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 686, "tenant_id": 3, "code": "task_collect_mode_both_arms", "label": "双臂", "pid": 684, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 687, "tenant_id": 3, "code": "task_collect_mode_left_arm", "label": "单臂_左", "pid": 684, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 688, "tenant_id": 3, "code": "task_collect_mode_right_arm", "label": "单臂_右", "pid": 684, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 689, "tenant_id": 3, "code": "sensor_type", "label": "传感器类型", "pid": 651, "level": 2, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 690, "tenant_id": 3, "code": "5*fisheye+3*rgbd", "label": "5*fisheye+3*rgbd", "pid": 689, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 691, "tenant_id": 3, "code": "7*fisheye+1*rgbd", "label": "7*fisheye+1*rgbd", "pid": 689, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 692, "tenant_id": 3, "code": "task_atomic_ability_single_arm", "label": "原子能力(通用)", "pid": 651, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 693, "tenant_id": 3, "code": "task_atomic_ability_both_arms", "label": "原子能力(其他)", "pid": 651, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 694, "tenant_id": 3, "code": "task_default_review_label", "label": "默认审核标签", "pid": 651, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 695, "tenant_id": 3, "code": "task_project", "label": "所属项目", "pid": 651, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 696, "tenant_id": 3, "code": "task_camera_rgbd", "label": "相机(rgbd)", "pid": 651, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 697, "tenant_id": 3, "code": "task_camera_rgbd_head", "label": "head", "pid": 696, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 698, "tenant_id": 3, "code": "task_camera_rgbd_hand_left", "label": "hand_left", "pid": 696, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 699, "tenant_id": 3, "code": "task_camera_rgbd_hand_right", "label": "hand_right", "pid": 696, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 700, "tenant_id": 3, "code": "task_camera_fisheye", "label": "相机(fisheye)", "pid": 651, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 701, "tenant_id": 3, "code": "task_camera_fisheye_head_center_fisheye", "label": "head_center_fisheye", "pid": 700, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 702, "tenant_id": 3, "code": "task_camera_fisheye_head_left_fisheye", "label": "head_left_fisheye", "pid": 700, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 703, "tenant_id": 3, "code": "task_camera_fisheye_head_right_fisheye", "label": "head_right_fisheye", "pid": 700, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 704, "tenant_id": 3, "code": "task_camera_fisheye_hand_left_fisheye", "label": "hand_left_fisheye", "pid": 700, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 705, "tenant_id": 3, "code": "task_camera_fisheye_hand_right_fisheye", "label": "hand_right_fisheye", "pid": 700, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 706, "tenant_id": 3, "code": "task_camera_fisheye_back_left_fisheye", "label": "back_left_fisheye", "pid": 700, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 707, "tenant_id": 3, "code": "task_camera_fisheye_back_right_fisheye", "label": "back_right_fisheye", "pid": 700, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 708, "tenant_id": 3, "code": "task_resource", "label": "物体标签", "pid": 0, "level": 1, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 709, "tenant_id": 3, "code": "task_resource_scene", "label": "所属场景", "pid": 708, "level": 2, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 710, "tenant_id": 3, "code": "task_resource_scene_工厂", "label": "工厂", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 711, "tenant_id": 3, "code": "task_resource_scene_家居", "label": "家居", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 712, "tenant_id": 3, "code": "task_resource_scene_超市", "label": "超市", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 713, "tenant_id": 3, "code": "task_resource_scene_咖啡厅", "label": "咖啡厅", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 714, "tenant_id": 3, "code": "task_resource_scene_餐厅", "label": "餐厅", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 715, "tenant_id": 3, "code": "task_resource_scene_厨房", "label": "厨房", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 716, "tenant_id": 3, "code": "task_resource_scene_奶茶店", "label": "奶茶店", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 717, "tenant_id": 3, "code": "task_resource_scene_浴室", "label": "浴室", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 718, "tenant_id": 3, "code": "task_resource_scene_客厅", "label": "客厅", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 719, "tenant_id": 3, "code": "task_resource_scene_烹饪", "label": "烹饪", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 720, "tenant_id": 3, "code": "task_resource_scene_Household", "label": "Household", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 721, "tenant_id": 3, "code": "task_resource_scene_会议室", "label": "会议室", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 722, "tenant_id": 3, "code": "task_resource_scene_办公区", "label": "办公区", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 723, "tenant_id": 3, "code": "task_resource_scene_办公", "label": "办公", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 724, "tenant_id": 3, "code": "task_resource_scene_卧室", "label": "卧室", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 725, "tenant_id": 3, "code": "task_resource_scene_工业", "label": "工业", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 726, "tenant_id": 3, "code": "task_resource_scene_商超", "label": "商超", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 727, "tenant_id": 3, "code": "task_resource_scene_办公室", "label": "办公室", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 728, "tenant_id": 3, "code": "task_resource_scene_餐饮", "label": "餐饮", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 729, "tenant_id": 3, "code": "task_resource_scene_书房", "label": "书房", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 730, "tenant_id": 3, "code": "task_resource_scene_其他", "label": "其他", "pid": 709, "level": 3, "status": 1, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 731, "tenant_id": 3, "code": "task_resource_kind", "label": "物体类型", "pid": 708, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 732, "tenant_id": 3, "code": "task_resource_region", "label": "所属区域", "pid": 708, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 733, "tenant_id": 3, "code": "task_resource_feature", "label": "其他特征", "pid": 708, "level": 2, "status": 4, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 734, "tenant_id": 3, "code": "task_resource_color", "label": "颜色特征", "pid": 708, "level": 2, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 735, "tenant_id": 3, "code": "task_resource_optical", "label": "光学特性", "pid": 708, "level": 2, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 736, "tenant_id": 3, "code": "task_resource_material", "label": "材质构成", "pid": 708, "level": 2, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 737, "tenant_id": 3, "code": "task_resource_morphology", "label": "形态特征", "pid": 708, "level": 2, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 738, "tenant_id": 3, "code": "task_resource_shape", "label": "形状特征", "pid": 708, "level": 2, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 739, "tenant_id": 3, "code": "task_resource_kinematic", "label": "运动特性", "pid": 708, "level": 2, "status": 2, "created_at": "2025-05-16 10:13:29", "updated_at": "2025-05-16 10:13:29"}, {"id": 829, "tenant_id": 3, "code": "task_atomic_ability_single_arm_抓取", "label": "抓取", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 11:11:13", "updated_at": "2025-05-16 11:11:13"}, {"id": 830, "tenant_id": 3, "code": "task_collect_ontology_A2", "label": "A2", "pid": 679, "level": 3, "status": 2, "created_at": "2025-05-16 11:13:44", "updated_at": "2025-05-16 11:13:44"}, {"id": 831, "tenant_id": 3, "code": "task_end_kind_z<PERSON><PERSON>_gripper_omnipicker", "label": "<PERSON><PERSON><PERSON>_gripper_omnipicker", "pid": 680, "level": 3, "status": 2, "created_at": "2025-05-16 11:14:12", "updated_at": "2025-05-16 11:14:12"}, {"id": 832, "tenant_id": 3, "code": "task_collect_ontology_A2D", "label": "A2D", "pid": 679, "level": 3, "status": 2, "created_at": "2025-05-16 11:16:06", "updated_at": "2025-05-16 11:16:06"}, {"id": 833, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Touch（触摸）", "label": "Touch（触摸）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 834, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Suction（吸附）", "label": "Suction（吸附）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 835, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Throw（投掷）", "label": "Throw（投掷）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 836, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Grasp（抓取）", "label": "<PERSON><PERSON><PERSON>（抓取）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 837, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Release（释放）", "label": "Release（释放）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 838, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Push（推）", "label": "<PERSON><PERSON>（推）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 839, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Pull（拉）", "label": "<PERSON><PERSON>（拉）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 840, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Rotate（旋转）", "label": "Rotate（旋转）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 841, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Lift（抬起）", "label": "Lift（抬起）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 842, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Lower（放下）", "label": "Lower（放下）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 843, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Press（按压）", "label": "Press（按压）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 844, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Swipe（滑动）", "label": "Swipe（滑动）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 845, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Tap（轻敲）", "label": "<PERSON><PERSON>（轻敲）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 846, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Point（指向）", "label": "Point（指向）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 847, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Flip（翻转）", "label": "Flip（翻转）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 848, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Slide（滑动）", "label": "Slide（滑动）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 849, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Pick（拾起）", "label": "Pick（拿起）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 850, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Place（放置）", "label": "Place（放置）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 851, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Shake（摇动）", "label": "Shake（摇动）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 852, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Open（打开）", "label": "Open（打开）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 853, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Close（关闭）", "label": "Close（关闭）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 854, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Turn（转动）", "label": "Turn（转动）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 855, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Scratch（刮擦）", "label": "<PERSON><PERSON><PERSON>（刮擦）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 856, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Pinch（捏）", "label": "<PERSON><PERSON>（捏）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 857, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Hold（握住）", "label": "Hold（握住）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 858, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Drop（丢下）", "label": "Drop（丢下）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 859, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Roll（滚动）", "label": "Roll（滚动）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 860, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Wave（挥手）", "label": "Wave（挥手）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 861, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Catch（接住）", "label": "Catch（接住）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 862, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Insert（插入）", "label": "Insert（插入）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 863, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Remove（移除）", "label": "Remove（移除）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 864, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Stretch（伸展）", "label": "<PERSON><PERSON><PERSON>（伸展）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 865, "tenant_id": 3, "code": "task_atomic_ability_single_arm_PressButton（按按钮）", "label": "PressButton（按按钮）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 866, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Transport（搬运）", "label": "Transport（搬运）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 867, "tenant_id": 3, "code": "task_atomic_ability_single_arm_PullApart（拉开）", "label": "<PERSON>ull<PERSON><PERSON><PERSON>（拉开）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 868, "tenant_id": 3, "code": "task_atomic_ability_single_arm_PushTogether（推合）", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>（推合）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 869, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Twist（扭转）", "label": "Twist（扭转）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 870, "tenant_id": 3, "code": "task_atomic_ability_single_arm_OpenJar（开罐）", "label": "OpenJar（开罐）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 871, "tenant_id": 3, "code": "task_atomic_ability_single_arm_CloseJar（关罐）", "label": "<PERSON><PERSON><PERSON>（关罐）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 872, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Fold（折叠）", "label": "Fold（折叠）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 873, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Unfold（展开）", "label": "Unfold（展开）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 874, "tenant_id": 3, "code": "task_atomic_ability_single_arm_LiftHeavyObject（举重）", "label": "LiftHeavyObject（举重）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 875, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Carry（携带）", "label": "Carry（携带）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 876, "tenant_id": 3, "code": "task_atomic_ability_single_arm_TurnWheel（转动轮子）", "label": "TurnWheel（转动轮子）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 877, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Clap（拍手）", "label": "Clap（拍手）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 878, "tenant_id": 3, "code": "task_atomic_ability_single_arm_OpenBox（打开盒子）", "label": "OpenBox（打开盒子）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 879, "tenant_id": 3, "code": "task_atomic_ability_single_arm_CloseBox（关闭盒子）", "label": "CloseBox（关闭盒子）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 880, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Tie（系紧）", "label": "<PERSON><PERSON>（系紧）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 881, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Untie（解开）", "label": "<PERSON><PERSON>（解开）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 882, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Stack（堆叠）", "label": "Stack（堆叠）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 883, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Unstack（拆卸）", "label": "Unstack（拆卸）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 884, "tenant_id": 3, "code": "task_atomic_ability_single_arm_HoldLargeObject（握持大物体）", "label": "HoldLargeObject（握持大物体）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 885, "tenant_id": 3, "code": "task_atomic_ability_single_arm_RollDough（擀面）", "label": "RollDough（擀面）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 886, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Knead（揉捏）", "label": "<PERSON><PERSON><PERSON>（揉捏）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 887, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Wipe（擦拭）", "label": "<PERSON><PERSON><PERSON>（擦拭）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 888, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Cut（切割）", "label": "Cut（切割）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 889, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Beat（敲打）", "label": "Beat（敲打）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 890, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Hammer（锤击）", "label": "<PERSON>（锤击）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 891, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Screw（拧紧）", "label": "<PERSON><PERSON>（拧紧）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 892, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Unscrew（拧松螺丝）", "label": "Unscrew（拧松螺丝）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 893, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Paint（涂刷）", "label": "Paint（涂刷）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 894, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Sweep（扫地）", "label": "Sweep（扫地）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 895, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Mop（拖地）", "label": "<PERSON><PERSON>（拖地）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 896, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Drill（钻孔）", "label": "Drill（钻孔）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 897, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Saw（锯）", "label": "<PERSON>（锯）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 898, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Stir（搅拌）", "label": "Stir（搅拌）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 899, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Chop（剁碎）", "label": "<PERSON><PERSON>（剁碎）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 900, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Whisk（搅打）", "label": "Whisk（搅打）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 901, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Scoop(舀)", "label": "<PERSON><PERSON>(舀)", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 902, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Peel（削皮）", "label": "Peel（削皮）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 903, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Brush（刷）", "label": "Brush（刷）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 904, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Hang（挂）", "label": "<PERSON>（挂）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 905, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Pour（倒）", "label": "Pour（倒）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 906, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Move（移动）", "label": "Move（移动）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 907, "tenant_id": 3, "code": "task_atomic_ability_single_arm_HandOver（传递）", "label": "HandOver（传递）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 908, "tenant_id": 3, "code": "task_atomic_ability_single_arm_dip（浸）", "label": "dip（浸）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 909, "tenant_id": 3, "code": "task_atomic_ability_single_arm_HandOut（递给）", "label": "HandOut（递给）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 910, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Iron（熨）", "label": "Iron（熨）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 911, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Scan（扫描）", "label": "<PERSON><PERSON>（扫描）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 912, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Straighten（整理）", "label": "<PERSON>en（整理）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 913, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Takeout（取出）", "label": "Takeout（取出）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 914, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Rinse（冲洗）", "label": "Rinse（冲洗）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 915, "tenant_id": 3, "code": "task_atomic_ability_single_arm_squeeze（挤）", "label": "squeeze（挤）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 916, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Unzip（拉开拉链）", "label": "Unzip（拉开拉链）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 917, "tenant_id": 3, "code": "task_atomic_ability_single_arm_water（浇水）", "label": "water（浇水）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 918, "tenant_id": 3, "code": "task_atomic_ability_single_arm_armretract（手臂内收）", "label": "arm<PERSON><PERSON><PERSON>（手臂内收）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 919, "tenant_id": 3, "code": "task_atomic_ability_single_arm_TakeOver（接过）", "label": "TakeOver（接过）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 920, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Uncap（拔）", "label": "Uncap（拔）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 921, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Unplug (拔插头)", "label": "Unplug (拔插头)", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 922, "tenant_id": 3, "code": "task_atomic_ability_single_arm_plug (插插头)", "label": "plug (插插头)", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 923, "tenant_id": 3, "code": "task_atomic_ability_single_arm_stamp（盖章）", "label": "stamp（盖章）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 924, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Stick（粘贴）", "label": "Stick（粘贴）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 925, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Other（其他）", "label": "Other（其他）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 926, "tenant_id": 3, "code": "task_atomic_ability_single_arm_PullOut（拔出）", "label": "Pull<PERSON>ut（拔出）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 927, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Pipette（吸取液体）", "label": "Pipette（吸取液体）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 928, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Cover（遮盖）", "label": "Cover（遮盖）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 929, "tenant_id": 3, "code": "task_atomic_ability_single_arm_vacuum（吸尘）", "label": "vacuum（吸尘）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 930, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Takedown（取下）", "label": "Takedown（取下）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 931, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Stir-Fry（翻炒）", "label": "Stir-Fry（翻炒）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 932, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Write（书写）", "label": "Write（书写）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 933, "tenant_id": 3, "code": "task_atomic_ability_single_arm_Clip(夹住）", "label": "Clip(夹住）", "pid": 692, "level": 3, "status": 2, "created_at": "2025-05-16 03:45:26", "updated_at": "2025-05-16 03:45:26"}, {"id": 960, "tenant_id": 3, "code": "task_end_kind_zhi<PERSON>_finger_SkillHandS6", "label": "zhi<PERSON>_finger_SkillHandS6", "pid": 680, "level": 3, "status": 2, "created_at": "2025-05-16 12:54:24", "updated_at": "2025-05-16 12:54:24"}, {"id": 961, "tenant_id": 3, "code": "task_end_kind_ctek_gripper_120s", "label": "ctek_gripper_120s", "pid": 680, "level": 3, "status": 2, "created_at": "2025-05-16 12:54:35", "updated_at": "2025-05-16 12:54:35"}, {"id": 962, "tenant_id": 3, "code": "task_end_kind_oymotion_finger_Roh", "label": "oymotion_finger_Roh", "pid": 680, "level": 3, "status": 2, "created_at": "2025-05-16 12:55:07", "updated_at": "2025-05-16 12:55:07"}, {"id": 1425, "tenant_id": 3, "code": "task_resource_kinematic_1", "label": "1", "pid": 739, "level": 3, "status": 2, "created_at": "2025-05-16 19:27:47", "updated_at": "2025-05-16 19:27:47"}, {"id": 1426, "tenant_id": 3, "code": "task_resource_shape_1", "label": "1", "pid": 738, "level": 3, "status": 2, "created_at": "2025-05-16 19:27:47", "updated_at": "2025-05-16 19:27:47"}, {"id": 1427, "tenant_id": 3, "code": "task_resource_morphology_1", "label": "1", "pid": 737, "level": 3, "status": 2, "created_at": "2025-05-16 19:27:48", "updated_at": "2025-05-16 19:27:48"}, {"id": 1428, "tenant_id": 3, "code": "task_resource_material_1", "label": "1", "pid": 736, "level": 3, "status": 2, "created_at": "2025-05-16 19:27:49", "updated_at": "2025-05-16 19:27:49"}, {"id": 1429, "tenant_id": 3, "code": "task_resource_optical_1", "label": "1", "pid": 735, "level": 3, "status": 2, "created_at": "2025-05-16 19:27:49", "updated_at": "2025-05-16 19:27:49"}, {"id": 1430, "tenant_id": 3, "code": "task_resource_color_1", "label": "1", "pid": 734, "level": 3, "status": 2, "created_at": "2025-05-16 19:27:50", "updated_at": "2025-05-16 19:27:50"}, {"id": 1431, "tenant_id": 3, "code": "task_resource_feature_1", "label": "1", "pid": 733, "level": 3, "status": 2, "created_at": "2025-05-16 19:27:50", "updated_at": "2025-05-16 19:27:50"}, {"id": 1432, "tenant_id": 3, "code": "task_resource_kind_1", "label": "1", "pid": 731, "level": 3, "status": 2, "created_at": "2025-05-16 19:34:46", "updated_at": "2025-05-16 19:34:46"}, {"id": 1433, "tenant_id": 3, "code": "task_resource_region_1", "label": "1", "pid": 732, "level": 3, "status": 2, "created_at": "2025-05-16 19:34:47", "updated_at": "2025-05-16 19:34:47"}, {"id": 2240, "tenant_id": 3, "code": "task_resource_kind_转子", "label": "转子", "pid": 731, "level": 3, "status": 2, "created_at": "2025-05-23 15:40:20", "updated_at": "2025-05-23 15:40:24"}, {"id": 2241, "tenant_id": 3, "code": "task_resource_material_铁", "label": "铁", "pid": 736, "level": 3, "status": 2, "created_at": "2025-05-23 15:40:57", "updated_at": "2025-05-23 15:40:57"}, {"id": 2250, "tenant_id": 3, "code": "task_resource_kind_手臂", "label": "手臂", "pid": 731, "level": 3, "status": 2, "created_at": "2025-05-28 08:56:30", "updated_at": "2025-05-28 08:57:58"}, {"id": 3050, "tenant_id": 3, "code": "task_collect_type", "label": "采集方式", "pid": 651, "level": 2, "status": 4, "created_at": "2025-06-06 09:57:06", "updated_at": "2025-06-06 09:57:06"}, {"id": 3065, "tenant_id": 3, "code": "task_collect_type_normal", "label": "常规", "pid": 3050, "level": 2, "status": 4, "created_at": "2025-06-06 09:57:06", "updated_at": "2025-06-06 09:57:06"}, {"id": 3080, "tenant_id": 3, "code": "task_collect_type_copilot", "label": "Copilot", "pid": 3050, "level": 2, "status": 4, "created_at": "2025-06-06 09:57:06", "updated_at": "2025-06-06 09:57:06"}, {"id": 3095, "tenant_id": 3, "code": "task_visual_range", "label": "任务视距", "pid": 651, "level": 2, "status": 4, "created_at": "2025-06-06 09:57:06", "updated_at": "2025-06-06 09:57:06"}, {"id": 3110, "tenant_id": 3, "code": "task_visual_range_inside", "label": "视距内", "pid": 3095, "level": 2, "status": 4, "created_at": "2025-06-06 09:57:06", "updated_at": "2025-06-06 09:57:06"}, {"id": 3125, "tenant_id": 3, "code": "task_visual_range_beyond", "label": "超视距", "pid": 3095, "level": 2, "status": 4, "created_at": "2025-06-06 09:57:06", "updated_at": "2025-06-06 09:57:06"}, {"id": 3585, "tenant_id": 3, "code": "task_resource_region_工厂", "label": "工厂", "pid": 732, "level": 3, "status": 2, "created_at": "2025-06-16 14:41:59", "updated_at": "2025-06-16 14:41:59"}, {"id": 3586, "tenant_id": 3, "code": "task_resource_kind_五金", "label": "五金", "pid": 731, "level": 3, "status": 2, "created_at": "2025-06-16 14:42:16", "updated_at": "2025-06-16 14:42:16"}, {"id": 3587, "tenant_id": 3, "code": "task_resource_kind_工具", "label": "工具", "pid": 731, "level": 3, "status": 2, "created_at": "2025-06-16 14:44:00", "updated_at": "2025-06-16 14:44:00"}, {"id": 4713, "tenant_id": 3, "code": "task_resource_kind_零件", "label": "零件", "pid": 731, "level": 3, "status": 2, "created_at": "2025-07-02 11:15:21", "updated_at": "2025-07-02 11:15:21"}]