package model

type ListItem struct {
	EpisodeID      uint   `json:"id"`
	TaskID         uint   `json:"task_id"`
	JobID          uint   `json:"job_id"`
	Path           string `json:"path"`
	Duration       int64  `json:"duration"`
	TaskType       string `json:"task_type"`
	ActionStepType int    `json:"action_step_type"`
}

type ResponseData struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	TraceID string `json:"traceid"`
	Data    struct {
		List     []ListItem `json:"List"`
		Total    int        `json:"total"`
		PageSize int        `json:"page_size"`
	} `json:"data"`
}
