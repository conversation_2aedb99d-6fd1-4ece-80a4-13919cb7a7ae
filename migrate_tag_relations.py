#!/usr/bin/env python3
"""
Tag Relations Migration Script

This script migrates tag relations from old environment to new environment by:
1. Mapping old tags to new tags by 'code'
2. Mapping old task_resources to new task_resources by 'name'
3. Creating new tag_rel records with correct IDs
"""

import json
import pymysql
from datetime import datetime
from typing import Dict, List, Optional, Any, Union


class TagRelationMigrator:
    def __init__(self, db_config: dict):
        """Initialize with database configuration"""
        self.db_config = db_config
        self.connection = None
        
        # Load data files
        self.old_tags = self._load_json('/Users/<USER>/Desktop/aim/old_tag.json')
        self.new_tags = self._load_json('/Users/<USER>/Desktop/aim/tag.json')
        self.old_task_resources = self._load_json('/Users/<USER>/Desktop/aim/aim_data_task_resource_origin.json')
        self.new_task_resources = self._load_json('/Users/<USER>/Desktop/aim/aim_data_task_resource_new.json')
        self.old_tag_rels = self._load_json('/Users/<USER>/Desktop/aim/aim_data_tag_rel.json')
        
        # Create mapping dictionaries
        self.tag_code_to_new_id = {}  # old_tag_code -> new_tag_id
        self.resource_name_to_new_id = {}  # resource_name -> new_resource_id
        self.old_tag_id_to_code = {}  # old_tag_id -> tag_code
        self.old_resource_id_to_name = {}  # old_resource_id -> resource_name
        
        self._build_mappings()
    
    def _load_json(self, filepath: str) -> List[dict]:
        """Load JSON file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading {filepath}: {e}")
            return []
    
    def _build_mappings(self):
        """Build mapping dictionaries"""
        print("Building mappings...")
        
        # Build tag mappings: code -> new_id
        for tag in self.new_tags:
            code = tag.get('code')
            tag_id = tag.get('id')
            if code and tag_id:
                self.tag_code_to_new_id[code] = tag_id
        
        # Build old tag id -> code mapping
        for tag in self.old_tags:
            tag_id = tag.get('id')
            code = tag.get('code')
            if tag_id and code:
                self.old_tag_id_to_code[tag_id] = code
        
        # Build task resource mappings: name -> new_id
        for resource in self.new_task_resources:
            name = resource.get('name')
            resource_id = resource.get('id')
            if name and resource_id:
                self.resource_name_to_new_id[name] = resource_id
        
        # Build old resource id -> name mapping
        for resource in self.old_task_resources:
            resource_id = resource.get('id')
            name = resource.get('name')
            if resource_id and name:
                self.old_resource_id_to_name[resource_id] = name
        
        print(f"Tag mappings: {len(self.tag_code_to_new_id)} tags")
        print(f"Resource mappings: {len(self.resource_name_to_new_id)} resources")
    
    def connect_db(self):
        """Connect to database using pymysql"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config.get('port', 3306),
                user=self.db_config['user'],
                password=self.db_config['password'],
                db=self.db_config['database'],
                charset=self.db_config.get('charset', 'utf8mb4'),
                cursorclass=pymysql.cursors.DictCursor
            )
            print("Database connected successfully")
        except Exception as e:
            print(f"Database connection failed: {e}")
            raise
    
    def disconnect_db(self):
        """Disconnect from database"""
        if self.connection:
            self.connection.close()
            print("Database disconnected")
    
    def get_new_tag_rel_records(self) -> List[dict]:
        """Generate new tag_rel records based on mappings"""
        new_records = []
        unmapped_tags = set()
        unmapped_resources = set()
        
        for old_rel in self.old_tag_rels:
            old_tag_id = old_rel.get('tag_id')
            old_resource_id = old_rel.get('resource_id')
            resource_type = old_rel.get('resource_type')
            tag_pid = old_rel.get('tag_pid')
            
            # Skip if not task_resource type
            if resource_type != 'task_resource':
                continue
            
            # Get tag code from old tag id
            tag_code = self.old_tag_id_to_code.get(old_tag_id)
            if not tag_code:
                unmapped_tags.add(old_tag_id)
                continue
            
            # Get new tag id from code
            new_tag_id = self.tag_code_to_new_id.get(tag_code)
            if not new_tag_id:
                unmapped_tags.add(f"{old_tag_id}({tag_code})")
                continue
            
            # Get resource name from old resource id
            resource_name = self.old_resource_id_to_name.get(old_resource_id)
            if not resource_name:
                unmapped_resources.add(old_resource_id)
                continue
            
            # Get new resource id from name
            new_resource_id = self.resource_name_to_new_id.get(resource_name)
            if not new_resource_id:
                unmapped_resources.add(f"{old_resource_id}({resource_name})")
                continue
            
            # Create new record
            new_record = {
                'tag_id': new_tag_id,
                'resource_id': new_resource_id,
                'resource_type': resource_type,
                'tag_pid': tag_pid,  # Keep original tag_pid for now
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            new_records.append(new_record)
        
        # Print unmapped items
        if unmapped_tags:
            print(f"Unmapped tags: {unmapped_tags}")
        if unmapped_resources:
            print(f"Unmapped resources: {unmapped_resources}")
        
        print(f"Generated {len(new_records)} new tag_rel records")
        return new_records
    
    def insert_tag_relations(self, records: List[dict], dry_run: bool = True):
        """Insert tag relations into database"""
        if not records:
            print("No records to insert")
            return
        
        if dry_run:
            print(f"DRY RUN: Would insert {len(records)} records")
            print("Sample records:")
            for i, record in enumerate(records[:3]):
                print(f"  {i+1}: {record}")
            return
        
        try:
            with self.connection.cursor() as cursor:
                # Prepare insert statement
                insert_sql = """
                INSERT INTO aim_data_tag_rel (tag_id, resource_id, resource_type, tag_pid, created_at, updated_at)
                VALUES (%(tag_id)s, %(resource_id)s, %(resource_type)s, %(tag_pid)s, %(created_at)s, %(updated_at)s)
                """
                
                # Execute batch insert
                cursor.executemany(insert_sql, records)
                self.connection.commit()
                
                print(f"Successfully inserted {cursor.rowcount} records")
            
        except Exception as e:
            print(f"Error inserting records: {e}")
            if self.connection:
                self.connection.rollback()
            raise
    
    def clear_existing_relations(self, dry_run: bool = True):
        """Clear existing tag relations for task_resource type"""
        if dry_run:
            print("DRY RUN: Would clear existing task_resource tag relations")
            return
        
        try:
            with self.connection.cursor() as cursor:
                delete_sql = "DELETE FROM aim_data_tag_rel WHERE resource_type = 'task_resource'"
                cursor.execute(delete_sql)
                self.connection.commit()
                print(f"Cleared {cursor.rowcount} existing tag relations")
        except Exception as e:
            print(f"Error clearing existing relations: {e}")
            if self.connection:
                self.connection.rollback()
            raise
    
    def migrate(self, clear_existing: bool = False, dry_run: bool = True):
        """Main migration method"""
        print("Starting tag relations migration...")
        
        # Generate new records
        new_records = self.get_new_tag_rel_records()
        
        if not new_records:
            print("No records to migrate")
            return
        
        # Connect to database
        self.connect_db()
        
        try:
            # Clear existing relations if requested
            if clear_existing:
                self.clear_existing_relations(dry_run)
            
            # Insert new relations
            self.insert_tag_relations(new_records, dry_run)
            
        finally:
            self.disconnect_db()
        
        print("Migration completed!")


def main():
    # 数据库配置
    # DB_CONFIG = {
    #     'host': 'your_host',        # 例如: 'localhost' 或 '*************'
    #     'port': 3306,               # MySQL 端口，通常是 3306
    #     'user': 'your_username',    # 数据库用户名
    #     'password': 'your_password', # 数据库密码
    #     'database': 'your_database', # 数据库名
    #     'charset': 'utf8mb4'
    # }

    DB_CONFIG = {
    'host': 'rm-bp1ltqb6yafzce28eao.mysql.rds.aliyuncs.com',
    'user': 'root',
    'password': 'Wa4P2h4j2kRYCR3BxV',
    'database': 'aim_data',
    'charset': 'utf8mb4'
}
    
    print("标签关系迁移工具")
    print("=" * 50)
    
    # 创建迁移器
    migrator = TagRelationMigrator(DB_CONFIG)
    
    # 首先以dry-run模式运行
    print("\n1. 正在执行DRY RUN (不会实际修改数据库)...")
    migrator.migrate(clear_existing=False, dry_run=True)
    
    print("\n" + "=" * 50)
    print("DRY RUN completed. Review the output above.")
    print("To execute the actual migration:")
    print("1. Update the database configuration in this script")
    print("2. Set dry_run=False in the migrate() call")
    print("3. Run the script again")


if __name__ == "__main__":
    main()
