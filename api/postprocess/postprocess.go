// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package postprocess

import (
	"context"

	"aim-data/api/postprocess/v1"
)

type IPostprocessV1 interface {
	GetEpisodeConfig(ctx context.Context, req *v1.GetEpisodeConfigReq) (res *v1.GetEpisodeConfigRes, err error)
}
