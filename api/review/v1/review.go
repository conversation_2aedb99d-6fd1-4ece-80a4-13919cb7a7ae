package v1

import (
	"aim-data/api"
	"aim-data/internal/model"

	"github.com/gogf/gf/v2/frame/g"
)

type GetReviewDetailReq struct {
	g.Meta        `path:"/collect/review/detail" method:"get" tags:"ReviewService" summary:"审核：详情，包含采集记录列表"`
	JobID         uint   `p:"job_id" v:"required" in:"query" dc:"动作id"`
	EpisodeID     uint   `p:"episode_id" in:"query" dc:"采集记录id"`
	AssignmentIDs []uint `p:"assignment_ids" in:"query" dc:"委派id"`
	Status        []uint `p:"status" in:"query" dc:"采集记录状态"`
	Checker       string `p:"checker" in:"query" dc:"审核员id"`
	DataType      uint   `p:"data_type" in:"query" dc:"数据类型 1常规 2非常规"`
	api.CommonPaginationReq
}

type GetReviewDetailRes struct {
	api.CommonPaginationRes
	IsInWhite bool               `json:"is_in_white"`
	Detail    model.ReviewDetail `json:"detail"`
}

type GetReviewStatusCountReq struct {
	g.Meta        `path:"/collect/review/status_count" method:"get" tags:"ReviewService" summary:"审核：状态枚举详情"`
	JobID         uint   `p:"job_id" v:"required" in:"query" dc:"动作id"`
	EpisodeID     uint   `p:"episode_id" in:"query" dc:"采集记录id"`
	AssignmentIDs []uint `p:"assignment_ids" in:"query" dc:"委派id"`
	Checker       string `p:"checker" in:"query" dc:"审核员id"`
	DataType      uint   `p:"data_type" in:"query" dc:"数据类型 1常规 2非常规"`
}

type GetReviewStatusCountRes struct {
	StatusCount model.StatusCountDetail `json:"status_count"`
}

type GetReviewDataReq struct {
	g.Meta    `path:"/collect/review/data/:episode_id" method:"get" tags:"ReviewService" summary:"审核：获取待审核的数据"`
	EpisodeID uint `p:"episode_id" v:"required" in:"path" dc:"采集记录id"`
}

type GetReviewDataRes struct {
	model.GetReviewOutput
}

type GetNextReviewDataReq struct {
	g.Meta       `path:"/collect/review/next/data/:job_id" method:"get" tags:"ReviewService" summary:"获取下一条待审核的数据"`
	JobID        uint `p:"job_id" v:"required" in:"path" dc:"动作ID"`
	AssignmentID uint `p:"assignment_id"`
}

type GetNextReviewDataRes struct {
	EpisodeID uint `json:"episode_id"`
}

type AddReviewReq struct {
	g.Meta                `path:"/collect/review" method:"post" tags:"ReviewService" summary:"审核：提交审核标注结果"`
	EpisodeID             uint   `p:"episode_id" v:"required" dc:"采集记录id"`
	Status                int    `p:"status" dc:"审核标注状态：6-人工审核通过，7-人工审核不通过，99-数据异常"`
	UsabilityScore        int    `p:"usability_score" d:"-1" dc:"数据可使用程度分类评分:1-优秀，2-可接受，3-差，默认-1未分类"`
	OverallScore          int    `p:"overall_score" d:"-1" dc:"整体评分:1-5, 默认-1未评分"`
	ErrorCountScore       int    `p:"error_count_score" d:"-1" dc:"错误量评分:1-5, 默认-1未评分"`
	SmoothnessScore       int    `p:"smoothness_score" d:"-1" dc:"流畅度评分:1-5, 默认-1未评分"`
	Comment               string `p:"comment" dc:"备注"`
	KeyFrame              string `p:"key_frame" dc:"关键帧标注"`
	ActionStepCalibration string `p:"action_step_calibration" dc:"动作步骤校准"`
	TaskID                uint   `p:"task_id" v:"required" dc:"task_id"`
}
type AddReviewRes struct {
	ID uint `json:"id"`
}

type GetReviewReq struct {
	g.Meta    `path:"/collect/review/:episode_id" method:"get" tags:"ReviewService" summary:"审核：获取审核标注结果"`
	EpisodeID uint `p:"episode_id" v:"required" in:"path" dc:"采集记录id"`
}

type GetReviewRes struct {
	Username              string `json:"username" dc:"审核员用户名"`
	Status                int    `json:"status" dc:"审核标注状态：6-人工审核通过，7-人工审核不通过，99-数据异常"`
	UsabilityScore        int    `json:"usability_score" dc:"可使用程度分类评分:1-优秀，2-可接受，3-差，默认-1未分类"`
	OverallScore          int    `json:"overall_score" dc:"整体评分:1-5, 默认-1未评分"`
	ErrorCountScore       int    `json:"error_count_score" dc:"错误量评分:1-5, 默认-1未评分"`
	SmoothnessScore       int    `json:"smoothness_score" dc:"流畅度评分:1-5, 默认-1未评分"`
	Comment               string `json:"comment" dc:"标注备注"`
	KeyFrame              string `json:"key_frame" dc:"关键帧标注"`
	ActionStepCalibration string `json:"action_step_calibration" dc:"动作步骤校准"`
}

type GetDeliverDetailReq struct {
	g.Meta `path:"/collect/review/deliver/:job_id" method:"get" tags:"ReviewService" summary:"一键交付下的采集员的待审核数据详情"`
	JobID  uint `p:"job_id" v:"required" in:"path" dc:"动作id"`
}
type GetDeliverDetailRes struct {
	List []*model.DeliverDetailOutput `json:"list"`
}

type OneClickDeliverReq struct {
	g.Meta        `path:"/collect/review/deliver" method:"put" tags:"ReviewService" summary:"一键交付"`
	AssignmentIDs []uint `p:"assignment_ids" v:"required" dc:"委派记录列表"`
}
type OneClickDeliverRes struct {
	Count int64 `json:"count"`
}

type GetNextEpisodeReq struct {
	g.Meta       `path:"/episode/next" method:"get" tags:"EpisodeService" summary:"获取下一条记录"`
	EpisodeID    uint   `p:"episode_id" v:"required#请输入当前记录ID"`
	Status       []uint `p:"status" dc:"episode状态筛选"`
	DataType     string `p:"data_type" dc:"数据类型筛选"`
	AssignmentID uint   `p:"assignment_id" dc:"委派ID筛选"`
	Checker      string `p:"checker" dc:"审核员筛选"`
}

type ListUserItem struct {
	Id             string   `json:"id"`
	DisplayName    string   `json:"display_name"`
	Phone          string   `json:"phone"`
	Username       string   `json:"username"`
	Roles          []string `json:"roles"`
	IsForbidden    bool     `json:"is_forbidden"`
	LastActiveTime string   `json:"last_active_time"`
	CreatedAt      string   `json:"created_at"`
	Source         uint     `json:"source"`
}

type GetNextEpisodeRes struct {
	EpisodeID uint `json:"episode_id"`
}

type ListReviewerReq struct {
	g.Meta `path:"/review/reviewer" method:"get" tags:"ReviewService" summary:"获取审核员名单"`
	TaskID uint `p:"task_id" in:"query" dc:"任务ID"`
}

type ListReviewerRes struct {
	List []ListUserItem `json:"list"`
}
