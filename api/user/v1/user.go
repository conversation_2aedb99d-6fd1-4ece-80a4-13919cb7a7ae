package v1

import (
	"aim-data/api"

	"github.com/gogf/gf/v2/frame/g"
)

type GetCurrentUserReq struct {
	g.Meta `path:"/user/current" method:"get" tags:"UserService" summary:"获取当前登录的用户信息"`
}

type GetCurrentUserRes struct {
	UserId      string   `json:"user_id"`
	Tenant      string   `json:"tenant"`
	Username    string   `json:"username"`
	DisplayName string   `json:"display_name"`
	Permissions []string `json:"permissions"`
	Roles       []string `json:"roles"`
}

type CreateUserReq struct {
	g.Meta      `path:"/user" method:"post" tags:"UserService" summary:"创建用户"`
	Username    string   `p:"username"`
	DisplayName string   `p:"display_name"`
	Phone       string   `p:"phone"`
	Password    string   `p:"password"`
	IsForbidden bool     `p:"is_forbidden"`
	Roles       []string `p:"roles"`
}

type CreateUserRes struct{}

type ListUserReq struct {
	g.Meta   `path:"/user" method:"get" tags:"UserService" summary:"检索用户"`
	Username string `p:"username" in:"query" dc:"用户账号"`
	//DisplayName string `p:"display_name" in:"query"`
	//Phone       string `p:"phone" in:"query"`
	Role   string `p:"role" in:"query" dc:"用户角色"`
	Status uint   `p:"status" in:"query" dc:"0:全部  1:启用  2:禁用"`
	//CreatedAt   string `p:"created_at" in:"query" dc:"按创建时间检索：2024-01-01 00:00:00,2024-02-02 23:59:59"`
	api.CommonPaginationReq
}

type ListUserRes struct {
	api.CommonPaginationRes
	List []ListUserItem `json:"list"`
}

type ListAllUserReq struct {
	g.Meta `path:"/user/all" method:"get" tags:"UserService" summary:"检索所有用户"`
}

type ListAllUserRes struct {
	List []ListUserItem `json:"list"`
}

type ListUserItem struct {
	Id             string   `json:"id"`
	DisplayName    string   `json:"display_name"`
	Phone          string   `json:"phone"`
	Username       string   `json:"username"`
	Roles          []string `json:"roles"`
	IsForbidden    bool     `json:"is_forbidden"`
	LastActiveTime string   `json:"last_active_time"`
	CreatedAt      string   `json:"created_at"`
	Source         uint     `json:"source"`
}

type UpdateUserReq struct {
	g.Meta      `path:"/user" method:"put" tags:"UserService" summary:"更新用户"`
	ID          string   `p:"id"`
	Username    string   `p:"username"`
	DisplayName string   `p:"display_name"`
	Phone       string   `p:"phone"`
	Password    string   `p:"password"`
	IsAdmin     bool     `p:"is_admin"`
	IsForbidden bool     `p:"is_forbidden"`
	Roles       []string `p:"roles"`
}

type UpdateUserRes struct{}

type DeleteUserReq struct {
	g.Meta    `path:"/user/delete" method:"post" tags:"UserService" summary:"删除用户"`
	Usernames []string `p:"usernames" dc:"待删除的用户列表"`
}

type DeleteUserRes struct{}
