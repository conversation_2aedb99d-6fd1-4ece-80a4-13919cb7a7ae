package internalv1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 数据集同步相关
type DatasetSyncStatusUpdateReq struct {
	g.Meta         `path:"/dataset/{id}/sync_status" method:"put" tags:"DatasetService" summary:"数据集同步状态更新"`
	Id             uint   `p:"id" v:"required" dc:"数据集ID"`
	Status         int    `json:"status" v:"in:4,5,6" dc:"数据集状态 5:同步失败 6:同步成功 7:同步失败"`
	SyncProgress   string `json:"sync_progress" dc:"同步进度 11/22"`
	ErrMsg         string `json:"err_msg" dc:"同步失败错误信息"`
	CheckpointName string `json:"checkpoint_name" dc:""`
}

type DatasetSyncStatusUpdateRes struct {
}

type DatasetMetadataReq struct {
	g.Meta `path:"/dataset/{id}/metadata" method:"get" tags:"DatasetService" summary:"数据集最新元数据获取"`
	Id     uint `p:"id" v:"required" dc:"数据集ID"`
}

type DatasetMetadataRes struct {
	SignedUrl  string     `json:"url" dc:"数据集元数据下载预签名地址"`
	Method     string     `json:"method"`
	NewDataGen bool       `json:"new_data_gen"`
	SyncStatus int        `json:"sync_status"`
	TaskIds    []uint     `json:"task_ids" dc:"数据集关联TaskId"`
	TaskInfos  []TaskInfo `json:"task_infos" `
}

type TaskInfo struct {
	TaskID       uint     `json:"task_id"`
	TaskName     string   `json:"task_name"`
	InitialScene string   `json:"initial_scene"`
	ActionText   []string `json:"action_text"`
}

type DatasetCreateVersionCBReq struct {
	g.Meta        `path:"/dataset/{id}/versions/{version}/callback" method:"put" tags:"DatasetService" summary:"数据集创建版本回调"`
	Id            uint     `p:"id" v:"required" dc:"数据集ID"`
	Version       string   `p:"version" dc:"版本"`
	EpisodeCnt    int      `json:"episode_cnt"`
	EpisodeIDList []uint64 `json:"episode_id_list"`
	SizeByte      int64    `json:"size_byte"`
	Status        int      `json:"status" dc:"数据集状态 1:创建中 2:创建完成 3:创建失败"`
	Path          string   `json:"path" dc:"数据版本存储路径"`
	ErrMsg        string   `json:"err_msg" dc:""`
}

type DatasetCreateVersionCBRes struct {
}
