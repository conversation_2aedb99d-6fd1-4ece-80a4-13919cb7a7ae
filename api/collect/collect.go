// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package collect

import (
	"context"

	"aim-data/api/collect/internalv1"
	v1 "aim-data/api/collect/v1"
)

type ICollectInternalv1 interface {
	GetTasks(ctx context.Context, req *internalv1.GetTasksReq) (res *internalv1.GetTasksRes, err error)
	SyncStatistic(ctx context.Context, req *internalv1.SyncStatisticReq) (res *internalv1.SyncStatisticRes, err error)
}

type ICollectV1 interface {
	AddAssignment(ctx context.Context, req *v1.AddAssignmentReq) (res *v1.AddAssignmentRes, err error)
	UpdateAssignment(ctx context.Context, req *v1.UpdateAssignmentReq) (res *v1.UpdateAssignmentRes, err error)
	GetAssignment(ctx context.Context, req *v1.GetAssignmentReq) (res *v1.GetAssignmentRes, err error)
	GetAssignments(ctx context.Context, req *v1.GetAssignmentsReq) (res *v1.GetAssignmentsRes, err error)
	UpdateEpisode(ctx context.Context, req *v1.UpdateEpisodeReq) (res *v1.UpdateEpisodeRes, err error)
	DeleteEpisode(ctx context.Context, req *v1.DeleteEpisodeReq) (res *v1.DeleteEpisodeRes, err error)
	GetAssignmentStat(ctx context.Context, req *v1.GetAssignmentStatReq) (res *v1.GetAssignmentStatRes, err error)
	DeleteJob(ctx context.Context, req *v1.DeleteJobReq) (res *v1.DeleteJobRes, err error)
	GetListTaskJob(ctx context.Context, req *v1.GetListTaskJobReq) (res *v1.GetListTaskJobRes, err error)
	CreateJob(ctx context.Context, req *v1.CreateJobReq) (res *v1.CreateJobRes, err error)
	GetJobDetail(ctx context.Context, req *v1.GetJobDetailReq) (res *v1.GetJobDetailRes, err error)
	UpdateJob(ctx context.Context, req *v1.UpdateJobReq) (res *v1.UpdateJobRes, err error)
	CloneJob(ctx context.Context, req *v1.CloneJobReq) (res *v1.CloneJobRes, err error)
	SpecialFrameVisual(ctx context.Context, req *v1.SpecialFrameVisualReq) (res *v1.SpecialFrameVisualRes, err error)
	DataOverview(ctx context.Context, req *v1.DataOverviewReq) (res *v1.DataOverviewRes, err error)
	TaskCollectCount(ctx context.Context, req *v1.TaskCollectCountReq) (res *v1.TaskCollectCountRes, err error)
	TaskCheckCount(ctx context.Context, req *v1.TaskCheckCountReq) (res *v1.TaskCheckCountRes, err error)
	DataCollectCount(ctx context.Context, req *v1.DataCollectCountReq) (res *v1.DataCollectCountRes, err error)
	DataCollectCountCSV(ctx context.Context, req *v1.DataCollectCountCSVReq) (res *v1.DataCollectCountCSVRes, err error)
	DataCollectRank(ctx context.Context, req *v1.DataCollectRankReq) (res *v1.DataCollectRankRes, err error)
	DataReason(ctx context.Context, req *v1.DataReasonReq) (res *v1.DataReasonRes, err error)
	DataReasonCSV(ctx context.Context, req *v1.DataReasonCSVReq) (res *v1.DataReasonCSVRes, err error)
	ValidDurationCSV(ctx context.Context, req *v1.ValidDurationCSVReq) (res *v1.ValidDurationCSVRes, err error)
	GetCollectStore(ctx context.Context, req *v1.GetCollectStoreReq) (res *v1.GetCollectStoreRes, err error)
	CreateEpisode(ctx context.Context, req *v1.CreateEpisodeReq) (res *v1.CreateEpisodeRes, err error)
	GetCollectEpisode(ctx context.Context, req *v1.GetCollectEpisodeReq) (res *v1.GetCollectEpisodeRes, err error)
	GetEpisode(ctx context.Context, req *v1.GetEpisodeReq) (res *v1.GetEpisodeRes, err error)
	CreateTask(ctx context.Context, req *v1.CreateTaskReq) (res *v1.CreateTaskRes, err error)
	UpdateTask(ctx context.Context, req *v1.UpdateTaskReq) (res *v1.UpdateTaskRes, err error)
	UpdateTaskStatus(ctx context.Context, req *v1.UpdateTaskStatusReq) (res *v1.UpdateTaskStatusRes, err error)
	UpdateTaskErrorTag(ctx context.Context, req *v1.UpdateTaskErrorTagReq) (res *v1.UpdateTaskErrorTagRes, err error)
	CopyTask(ctx context.Context, req *v1.CopyTaskReq) (res *v1.CopyTaskRes, err error)
	DeleteTask(ctx context.Context, req *v1.DeleteTaskReq) (res *v1.DeleteTaskRes, err error)
	GetListTask(ctx context.Context, req *v1.GetListTaskReq) (res *v1.GetListTaskRes, err error)
	GetTasks(ctx context.Context, req *v1.GetTasksReq) (res *v1.GetTasksRes, err error)
	GetSimpleTasks(ctx context.Context, req *v1.GetSimpleTasksReq) (res *v1.GetSimpleTasksRes, err error)
	GetWithoutProjTasks(ctx context.Context, req *v1.GetWithoutProjTasksReq) (res *v1.GetWithoutProjTasksRes, err error)
	GetTaskDetail(ctx context.Context, req *v1.GetTaskDetailReq) (res *v1.GetTaskDetailRes, err error)
	GetAllTask(ctx context.Context, req *v1.GetAllTaskReq) (res *v1.GetAllTaskRes, err error)
	AddTaskUser(ctx context.Context, req *v1.AddTaskUserReq) (res *v1.AddTaskUserRes, err error)
	UpdateTaskUser(ctx context.Context, req *v1.UpdateTaskUserReq) (res *v1.UpdateTaskUserRes, err error)
	GetListTaskUser(ctx context.Context, req *v1.GetListTaskUserReq) (res *v1.GetListTaskUserRes, err error)
	GetListTaskUserAll(ctx context.Context, req *v1.GetListTaskUserAllReq) (res *v1.GetListTaskUserAllRes, err error)
	DownloadTaskReviewPassData(ctx context.Context, req *v1.DownloadTaskReviewPassDataReq) (res *v1.DownloadTaskReviewPassDataRes, err error)
	DownloadTaskReviewPassDataCheck(ctx context.Context, req *v1.DownloadTaskReviewPassDataCheckReq) (res *v1.DownloadTaskReviewPassDataCheckRes, err error)
	DownloadTaskReviewData(ctx context.Context, req *v1.DownloadTaskReviewDataReq) (res *v1.DownloadTaskReviewDataRes, err error)
	DownloadTaskReviewDataCheck(ctx context.Context, req *v1.DownloadTaskReviewDataCheckReq) (res *v1.DownloadTaskReviewDataCheckRes, err error)
	GetTaskStatusCount(ctx context.Context, req *v1.GetTaskStatusCountReq) (res *v1.GetTaskStatusCountRes, err error)
	GetSimpleListTaskJob(ctx context.Context, req *v1.GetSimpleListTaskJobReq) (res *v1.GetSimpleListTaskJobRes, err error)
	CreateTaskResource(ctx context.Context, req *v1.CreateTaskResourceReq) (res *v1.CreateTaskResourceRes, err error)
	UpdateTaskResource(ctx context.Context, req *v1.UpdateTaskResourceReq) (res *v1.UpdateTaskResourceRes, err error)
	GetOneTaskResource(ctx context.Context, req *v1.GetOneTaskResourceReq) (res *v1.GetOneTaskResourceRes, err error)
	GetTaskResource(ctx context.Context, req *v1.GetTaskResourceReq) (res *v1.GetTaskResourceRes, err error)
	DeleteTaskResource(ctx context.Context, req *v1.DeleteTaskResourceReq) (res *v1.DeleteTaskResourceRes, err error)
	GetAllTaskResource(ctx context.Context, req *v1.GetAllTaskResourceReq) (res *v1.GetAllTaskResourceRes, err error)
}
