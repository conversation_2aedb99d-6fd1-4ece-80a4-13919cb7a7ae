import pymysql

# DSN/URI 连接字符串
# dsn = "mysql:agibot:agibot2406@tcp(**************:31615)/aim_data?loc=Local&parseTime=true"
#
# conn = pymysql.connect(dsn)
conn = pymysql.connect(
    host='**************',
    port=31615,
    user='agibot',
    password='agibot2406',
    db='aim_data',
    charset='utf8mb4'
)

cur = conn.cursor()
cur.execute("SELECT output_result FROM task_data_import_export WHERE id=%s", (1,))
row = cur.fetchone()
if row and row[0]:
    with open('output.json', 'w', encoding='utf-8') as f:
        f.write(row[0])
    print("导出成功，已保存为 output.json")
else:
    print("未查询到数据或内容为空")
cur.close()
