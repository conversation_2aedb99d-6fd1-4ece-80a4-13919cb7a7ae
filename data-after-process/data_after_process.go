package data_after_process

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"sync"
	"time"

	"aim_data_worker/logger"
	"aim_data_worker/model"
)

var (
	consumerBaseURL        string
	aimDataAfterProcessUrl string
)

func init() {
	getEnv()
}

func getEnv() {
	consumerBaseURL = os.Getenv("DATA_CONSUMER_URL")
	if consumerBaseURL == "" {
		//baseURL = "http://localhost:5000"
		consumerBaseURL = "http://consumer.test.agibot.com"
	}
	aimDataAfterProcessUrl = os.Getenv("AIM_DATA_AFTER_PROCESS_API_URL")
	if aimDataAfterProcessUrl == "" {
		//baseURL = "http://localhost:9000/api/v1/collect/episode"
		aimDataAfterProcessUrl = "https://aimdata.test.agibot.com/api/v1/collect/episode"
	}
}
func threadTask(arg map[string]interface{}) (res bool) {
	logger.CustomLog.Debugf("接收到的arg参数:%s", arg)
	consumerURL := fmt.Sprintf("%s/internal_trigger", consumerBaseURL)
	payload, err := json.Marshal(map[string]interface{}{
		"input_parameters": arg,
		//"is_force":         3,
	})
	if err != nil {
		logger.CustomLog.Error("JSON序列化失败:", err)
		return
	}
	req, err := http.NewRequest("POST", consumerURL, bytes.NewBuffer(payload))
	if err != nil {
		logger.CustomLog.Error("创建请求失败:", err)
		return
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.CustomLog.Error("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()
	all, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.CustomLog.Error("解析結果出錯:", err)
		return
	}
	//logger.CustomLog.Info(string(all))
	// 定义一个 map 用于存储解析后的数据
	var data map[string]interface{}

	// 解析 JSON 字符串
	err = json.Unmarshal(all, &data)
	if err != nil {
		logger.CustomLog.Error("Error parsing JSON:", err)
		return
	}

	code, ok := data["code"].(float64)
	if !ok {
		logger.CustomLog.Error("Error converting code to int")
		return
	}
	if int(code) == 200 {
		res = true
	}
	return
}

func getTimeRange(day int, hour int) string {
	// 获取当前时间
	now := time.Now()

	// 计算要向前推移的时间
	duration := time.Duration(day*24+hour) * time.Hour
	start := now.Add(-duration)

	// 格式化时间
	const layout = "2006-01-02 15:04:05"
	startFormatted := start.Format(layout)
	nowFormatted := now.Format(layout)

	return fmt.Sprintf("%s,%s", startFormatted, nowFormatted)
}

func dataProcess(queryParams url.Values) {
	fullURL := fmt.Sprintf("%s?%s", aimDataAfterProcessUrl, queryParams.Encode())
	logger.CustomLog.Info("开始数据后处理,请求路径为", fullURL)

	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		logger.CustomLog.Error("创建请求失败:", err)
		return
	}
	req.Header.Set("X-CLIENT-TOKEN", "agibot")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.CustomLog.Error("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()
	var data model.ResponseData
	if err := json.NewDecoder(resp.Body).Decode(&data); err != nil {
		logger.CustomLog.Error("发送请求失败:", err)
		return
	}
	list := data.Data.List

	args := make([]map[string]interface{}, len(list))
	//predictArgs := make([]map[string]interface{}, len(list))
	for i, item := range list {
		args[i] = map[string]interface{}{
			"episode_id":       item.EpisodeID,
			"path":             item.Path,
			"duration":         item.Duration,
			"task_type":        item.TaskType,
			"action_step_type": item.ActionStepType,
		}
		//if item.ActionStepType == consts.ACTION_STEP_TYPE_RANDOM {
		//	predictArgs = append(predictArgs, map[string]interface{}{
		//		"deployment_name": "annotations-vlm",
		//		"unique_key":      item.EpisodeID,
		//		"is_force":        3,
		//		"parameters": map[string]interface{}{
		//			"job_paths": []string{item.Path},
		//			"path": fmt.Sprintf(
		//				"annotations/vlm/predict-results/%d/%d/%d",
		//				item.TaskID, item.JobID, item.EpisodeID),
		//		},
		//	})
		//}
	}
	logger.CustomLog.Info("共获取待处理数据", len(args), "条")
	var wg sync.WaitGroup
	for _, arg := range args {
		wg.Add(1)
		go func(arg map[string]interface{}) {
			defer wg.Done()
			threadTask(arg)
		}(arg)
	}
}

func DataAfterProcess() {
	queryParams := url.Values{}
	queryParams.Set("order", "created_at desc")
	queryParams.Set("PageSize", "500")
	queryParams.Set("created_at", getTimeRange(0, 120))
	dataProcess(queryParams)

	queryParamsStatusFail := url.Values{}
	queryParamsStatusFail.Set("order", "created_at desc")
	queryParamsStatusFail.Set("PageSize", "500")
	queryParamsStatusFail.Set("created_at", getTimeRange(0, 120))
	queryParamsStatusFail.Set("status", "11")
	dataProcess(queryParamsStatusFail)
}
