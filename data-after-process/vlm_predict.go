package data_after_process

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"aim_data_worker/logger"
)

func vlmPredict(arg map[string]interface{}) {
	logger.CustomLog.Debugf("接收到的arg参数:%s", arg)
	consumerURL := fmt.Sprintf("%s/internal_trigger", consumerBaseURL)
	payload, err := json.Marshal(arg)
	if err != nil {
		logger.CustomLog.Error("JSON序列化失败:", err)
		return
	}
	req, err := http.NewRequest("POST", consumerURL, bytes.NewBuffer(payload))
	if err != nil {
		logger.CustomLog.Error("创建请求失败:", err)
		return
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.CustomLog.Error("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()
	all, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.CustomLog.Error("解析結果出錯:", err)
		return
	}
	//logger.CustomLog.Info(string(all))
	// 定义一个 map 用于存储解析后的数据
	var data map[string]interface{}

	// 解析 JSON 字符串
	err = json.Unmarshal(all, &data)
	if err != nil {
		logger.CustomLog.Error("Error parsing JSON:", err)
		return
	}
	logger.CustomLog.Debugf("vlm predict res %v", data)
}
