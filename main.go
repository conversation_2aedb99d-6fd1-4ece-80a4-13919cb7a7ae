package main

import (
	"os"
	"time"

	aimDataCronJob "aim_data_worker/aim-data-cron-job"
	dataAfterProcess "aim_data_worker/data-after-process"
	"aim_data_worker/logger"

	"github.com/robfig/cron/v3"
)

func main() {
	c := cron.New()

	initialDelay(10)
	aimDataCronJob.SyncDeviceRecord()
	dataAfterProcess.DataAfterProcess()  // 注释掉初始调用

	_, err := c.AddFunc("@every 5m", aimDataCronJob.SyncDeviceRecord)
	if err != nil {
		logger.CustomLog.Error("Error SyncDeviceRecord:", err)
		return
	}

	_, err = c.AddFunc("@every 5m", aimDataCronJob.SyncDatasetCreate)
	if err != nil {
		logger.CustomLog.Error("Error SyncDatasetCreate:", err)
		return
	}

	// 添加统计数据同步定时任务
	_, err = c.AddFunc("@every 5m", aimDataCronJob.SyncStatistic)
	if err != nil {
		logger.CustomLog.Error("Error SyncStatistic:", err)
		return
	}

	// 数据后处理定时任务 - 注释掉整个后处理部分

	isRunDataAfterProcess := os.Getenv("IS_RUN_DATA_AFTER_PROCESS")
	if isRunDataAfterProcess == "" {
		isRunDataAfterProcess = "false"
	}
	if isRunDataAfterProcess == "true" {
		logger.CustomLog.Info("数据后处理开关已打开,每15分钟执行一次")
		_, err = c.AddFunc("@every 5m", dataAfterProcess.DataAfterProcess)
		if err != nil {
			logger.CustomLog.Error("Error adding task:", err)
			return
		}
	} else {
		logger.CustomLog.Error("数据后处理开关未打开")
	}

	// 启动定时任务，带有1分钟的初始延迟
	c.Start()
	//
	// 主程序继续执行，定时任务会在后台独立运行
	select {}
}

func initialDelay(delaySeconds int) {
	logger.CustomLog.Infof("定时任务启动倒计时%d秒", delaySeconds)
	for i := 0; i < delaySeconds; i++ {
		time.Sleep(1 * time.Second)
		logger.CustomLog.Info(delaySeconds - i)
	}
	logger.CustomLog.Infof("定时任务启动")
}
