variables:
  API_SERVER_NAME: "aim-data-cron-worker"

  PROJECT_DOCKERFILE: "Dockerfile"
  DOCKER_NAME: $DOCKER_REGISTRY_HOST/agibot-tech/aim-data-worker
  IMAGE_NAME: $DOCKER_NAME:$CI_COMMIT_SHORT_SHA
  TAG_IMAGE_NAME: $DOCKER_NAME:$CI_COMMIT_TAG

  NAMESPACE: "aim-data"
  DEPLOY_NAME: "aim-data-cron-worker"
  SKIP_LINT: "true"
  SKIP_UNIT_TESTING: "true"
  SKIP_BUILD: "true"

include:
  - project: 'agibot_cloud/aim_stage/ci_template'
    file: '.go-gitlab-ci.yml'
    ref: 'main'

image-test:
  script:
    - docker build -t ${IMAGE_NAME}  .
    - docker push ${IMAGE_NAME}
    - echo "test image build finish--${IMAGE_NAME}"
image-prod:
  script:
    - echo  "$CI_COMMIT_BRANCH  $SKIP_DEPLOY  $CI_COMMIT_BRANCH == "master"  $CI_COMMIT_TAG "
    - docker build -t $TAG_IMAGE_NAME .
    - docker push $TAG_IMAGE_NAME
    - echo "test image build finish--$TAG_IMAGE_NAME"
deploy-stage:
  rules:
    - if: '$SKIP_DEPLOY != "true" && $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/   &&  $CI_DEFAULT_BRANCH  != "master"'
deploy-prod:
  rules:
    - if: '$SKIP_DEPLOY != "true" &&  $CI_DEFAULT_BRANCH  == "master" &&  $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+.*$/'
