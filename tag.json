[{"id": 1, "tenant_id": 1, "code": "task", "label": "任务标签", "pid": 0, "level": 1, "status": 1, "created_at": "2024-10-12 16:58:02", "updated_at": "2024-12-06 10:07:46"}, {"id": 2, "tenant_id": 1, "code": "task_stage", "label": "任务用途", "pid": 1, "level": 2, "status": 1, "created_at": "2024-10-12 16:58:02", "updated_at": "2024-10-12 16:58:02"}, {"id": 3, "tenant_id": 1, "code": "task_stage_prod", "label": "正式采集", "pid": 2, "level": 3, "status": 1, "created_at": "2024-10-12 16:58:02", "updated_at": "2025-01-08 04:03:31"}, {"id": 4, "tenant_id": 1, "code": "task_stage_test", "label": "开发测试", "pid": 2, "level": 3, "status": 1, "created_at": "2024-10-12 16:58:02", "updated_at": "2025-01-08 04:03:31"}, {"id": 5, "tenant_id": 1, "code": "task_stage_pilot_prod", "label": "运营试采", "pid": 2, "level": 3, "status": 1, "created_at": "2024-10-12 16:58:02", "updated_at": "2025-01-08 04:03:31"}, {"id": 6, "tenant_id": 1, "code": "task_stage_simulation", "label": "仿真评测", "pid": 2, "level": 3, "status": 1, "created_at": "2024-10-12 16:58:02", "updated_at": "2024-10-12 16:58:02"}, {"id": 241, "tenant_id": 1, "code": "task_resource", "label": "物体标签", "pid": 0, "level": 1, "status": 1, "created_at": "2024-12-06 10:07:45", "updated_at": "2024-12-06 10:07:45"}, {"id": 242, "tenant_id": 1, "code": "task_resource_scene", "label": "所属场景", "pid": 241, "level": 2, "status": 1, "created_at": "2024-12-06 10:07:45", "updated_at": "2024-12-06 10:07:45"}, {"id": 243, "tenant_id": 1, "code": "task_resource_kind", "label": "物体类型", "pid": 241, "level": 2, "status": 4, "created_at": "2024-12-06 10:07:45", "updated_at": "2024-12-06 10:07:45"}, {"id": 244, "tenant_id": 1, "code": "task_resource_region", "label": "所属区域", "pid": 241, "level": 2, "status": 4, "created_at": "2024-12-06 10:07:45", "updated_at": "2024-12-06 10:07:45"}, {"id": 245, "tenant_id": 1, "code": "task_resource_feature", "label": "其他特征", "pid": 241, "level": 2, "status": 4, "created_at": "2024-12-06 10:07:46", "updated_at": "2025-04-28 04:04:52"}, {"id": 246, "tenant_id": 1, "code": "task_scene_classification", "label": "场景分类", "pid": 1, "level": 2, "status": 4, "created_at": "2024-12-06 10:07:46", "updated_at": "2024-12-06 10:07:46"}, {"id": 247, "tenant_id": 1, "code": "task_collect_ontology", "label": "采集本体", "pid": 1, "level": 2, "status": 4, "created_at": "2024-12-06 10:07:46", "updated_at": "2024-12-06 10:07:46"}, {"id": 248, "tenant_id": 1, "code": "task_end_kind", "label": "末端类型", "pid": 1, "level": 2, "status": 4, "created_at": "2024-12-06 10:07:46", "updated_at": "2024-12-06 10:07:46"}, {"id": 249, "tenant_id": 1, "code": "task_remote_control_kind", "label": "遥操类型", "pid": 1, "level": 2, "status": 1, "created_at": "2024-12-06 10:07:46", "updated_at": "2024-12-06 10:07:46"}, {"id": 250, "tenant_id": 1, "code": "task_remote_control_kind_vr", "label": "VR", "pid": 249, "level": 3, "status": 1, "created_at": "2024-12-06 10:07:46", "updated_at": "2024-12-06 10:07:46"}, {"id": 251, "tenant_id": 1, "code": "task_remote_control_kind_motion_capture", "label": "动捕", "pid": 249, "level": 3, "status": 1, "created_at": "2024-12-06 10:07:47", "updated_at": "2024-12-06 10:07:47"}, {"id": 252, "tenant_id": 1, "code": "task_collect_mode", "label": "采集模式", "pid": 1, "level": 2, "status": 1, "created_at": "2024-12-06 10:07:47", "updated_at": "2024-12-06 10:07:47"}, {"id": 253, "tenant_id": 1, "code": "task_collect_mode_full_body", "label": "全身", "pid": 252, "level": 3, "status": 1, "created_at": "2024-12-06 10:07:47", "updated_at": "2024-12-06 10:07:47"}, {"id": 254, "tenant_id": 1, "code": "task_collect_mode_both_arms", "label": "双臂", "pid": 252, "level": 3, "status": 1, "created_at": "2024-12-06 10:07:47", "updated_at": "2024-12-06 10:07:47"}, {"id": 255, "tenant_id": 1, "code": "task_collect_mode_left_arm", "label": "单臂_左", "pid": 252, "level": 3, "status": 1, "created_at": "2024-12-06 10:07:47", "updated_at": "2024-12-06 10:07:47"}, {"id": 256, "tenant_id": 1, "code": "task_collect_mode_right_arm", "label": "单臂_右", "pid": 252, "level": 3, "status": 1, "created_at": "2024-12-06 10:07:47", "updated_at": "2024-12-06 10:07:47"}, {"id": 257, "tenant_id": 1, "code": "sensor_type", "label": "传感器类型", "pid": 1, "level": 2, "status": 1, "created_at": "2024-12-06 10:07:47", "updated_at": "2024-12-06 10:07:47"}, {"id": 258, "tenant_id": 1, "code": "5*fisheye+3*rgbd", "label": "5*fisheye+3*rgbd", "pid": 257, "level": 3, "status": 1, "created_at": "2024-12-06 10:07:47", "updated_at": "2024-12-06 10:07:47"}, {"id": 259, "tenant_id": 1, "code": "7*fisheye+1*rgbd", "label": "7*fisheye+1*rgbd", "pid": 257, "level": 3, "status": 1, "created_at": "2024-12-06 10:07:47", "updated_at": "2024-12-06 10:07:47"}, {"id": 260, "tenant_id": 1, "code": "task_atomic_ability_single_arm", "label": "原子能力(通用)", "pid": 1, "level": 2, "status": 4, "created_at": "2024-12-06 10:07:48", "updated_at": "2024-12-06 10:07:48"}, {"id": 261, "tenant_id": 1, "code": "task_atomic_ability_both_arms", "label": "原子能力(其他)", "pid": 1, "level": 2, "status": 4, "created_at": "2024-12-06 10:07:48", "updated_at": "2024-12-06 10:07:48"}, {"id": 262, "tenant_id": 1, "code": "task_resource_kind_日用品", "label": "日用品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 18:33:52", "updated_at": "2024-12-06 18:33:52"}, {"id": 263, "tenant_id": 1, "code": "task_resource_kind_食品", "label": "食品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 18:34:05", "updated_at": "2024-12-06 18:34:05"}, {"id": 264, "tenant_id": 1, "code": "task_resource_kind_洗护用品", "label": "洗护用品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 18:34:14", "updated_at": "2024-12-06 18:34:14"}, {"id": 265, "tenant_id": 1, "code": "task_resource_kind_餐具", "label": "餐具", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 18:34:24", "updated_at": "2024-12-06 18:34:24"}, {"id": 266, "tenant_id": 1, "code": "task_resource_kind_清洁用品", "label": "清洁用品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 18:34:33", "updated_at": "2024-12-06 18:34:33"}, {"id": 267, "tenant_id": 1, "code": "task_resource_region_工厂", "label": "工厂", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-06 18:34:45", "updated_at": "2024-12-06 18:34:45"}, {"id": 268, "tenant_id": 1, "code": "task_resource_region_家居", "label": "家居", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-06 18:34:55", "updated_at": "2024-12-06 18:34:55"}, {"id": 269, "tenant_id": 1, "code": "task_resource_region_商超", "label": "商超", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-06 18:35:02", "updated_at": "2024-12-06 18:35:02"}, {"id": 270, "tenant_id": 1, "code": "task_resource_feature_其他", "label": "其他", "pid": 245, "level": 3, "status": 2, "created_at": "2024-12-06 18:35:23", "updated_at": "2024-12-06 18:35:23"}, {"id": 271, "tenant_id": 1, "code": "task_resource_scene_工厂", "label": "工厂", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-06 18:35:39", "updated_at": "2024-12-06 18:35:39"}, {"id": 272, "tenant_id": 1, "code": "task_scene_classification_工厂", "label": "工厂", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-06 18:35:39", "updated_at": "2024-12-06 18:35:39"}, {"id": 273, "tenant_id": 1, "code": "task_resource_scene_家居", "label": "家居", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-06 18:35:48", "updated_at": "2024-12-06 18:35:48"}, {"id": 274, "tenant_id": 1, "code": "task_scene_classification_家居", "label": "家居", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-06 18:35:48", "updated_at": "2024-12-06 18:35:48"}, {"id": 275, "tenant_id": 1, "code": "task_resource_scene_超市", "label": "超市", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-06 18:35:56", "updated_at": "2024-12-06 18:35:56"}, {"id": 276, "tenant_id": 1, "code": "task_scene_classification_超市", "label": "超市", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-06 18:35:56", "updated_at": "2024-12-06 18:35:56"}, {"id": 277, "tenant_id": 1, "code": "task_resource_scene_咖啡厅", "label": "咖啡厅", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-06 18:36:04", "updated_at": "2024-12-06 18:36:04"}, {"id": 278, "tenant_id": 1, "code": "task_scene_classification_咖啡厅", "label": "咖啡厅", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-06 18:36:04", "updated_at": "2024-12-06 18:36:04"}, {"id": 279, "tenant_id": 1, "code": "task_resource_scene_餐厅", "label": "餐厅", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-06 18:36:12", "updated_at": "2024-12-06 18:36:12"}, {"id": 280, "tenant_id": 1, "code": "task_scene_classification_餐厅", "label": "餐厅", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-06 18:36:12", "updated_at": "2024-12-06 18:36:12"}, {"id": 281, "tenant_id": 1, "code": "task_resource_scene_厨房", "label": "厨房", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-06 18:36:24", "updated_at": "2024-12-06 18:36:24"}, {"id": 282, "tenant_id": 1, "code": "task_scene_classification_厨房", "label": "厨房", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-06 18:36:24", "updated_at": "2024-12-06 18:36:24"}, {"id": 283, "tenant_id": 1, "code": "task_resource_scene_奶茶店", "label": "奶茶店", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-06 18:36:32", "updated_at": "2024-12-06 18:36:32"}, {"id": 284, "tenant_id": 1, "code": "task_scene_classification_奶茶店", "label": "奶茶店", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-06 18:36:32", "updated_at": "2024-12-06 18:36:32"}, {"id": 285, "tenant_id": 1, "code": "task_resource_scene_浴室", "label": "浴室", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-06 18:36:40", "updated_at": "2024-12-06 18:36:40"}, {"id": 286, "tenant_id": 1, "code": "task_scene_classification_浴室", "label": "浴室", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-06 18:36:40", "updated_at": "2024-12-06 18:36:40"}, {"id": 287, "tenant_id": 1, "code": "task_resource_scene_客厅", "label": "客厅", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-06 18:36:48", "updated_at": "2024-12-06 18:36:48"}, {"id": 288, "tenant_id": 1, "code": "task_scene_classification_客厅", "label": "客厅", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-06 18:36:48", "updated_at": "2024-12-06 18:36:48"}, {"id": 289, "tenant_id": 1, "code": "task_collect_ontology_A2D", "label": "A2D", "pid": 247, "level": 3, "status": 2, "created_at": "2024-12-06 18:37:00", "updated_at": "2024-12-06 18:37:00"}, {"id": 290, "tenant_id": 1, "code": "task_end_kind_灵巧手：oymotion_finger_Roh", "label": "oymotion_finger_Roh", "pid": 248, "level": 3, "status": 2, "created_at": "2024-12-06 18:37:09", "updated_at": "2024-12-17 10:49:51"}, {"id": 291, "tenant_id": 1, "code": "task_end_kind_夹爪：ctek_gripper_120s", "label": "ctek_gripper_120s", "pid": 248, "level": 3, "status": 2, "created_at": "2024-12-06 18:37:20", "updated_at": "2024-12-17 10:49:51"}, {"id": 292, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Touch（触摸）", "label": "Touch（触摸）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:38:22", "updated_at": "2024-12-06 18:38:22"}, {"id": 293, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Suction（吸附）", "label": "Suction（吸附）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:38:30", "updated_at": "2024-12-06 18:38:30"}, {"id": 294, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Throw（投掷）", "label": "Throw（投掷）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:38:38", "updated_at": "2024-12-06 18:38:38"}, {"id": 295, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Grasp（抓取）", "label": "<PERSON><PERSON><PERSON>（抓取）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:38:49", "updated_at": "2024-12-06 18:38:49"}, {"id": 296, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Release（释放）", "label": "Release（释放）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:38:56", "updated_at": "2024-12-06 18:38:56"}, {"id": 297, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Push（推）", "label": "<PERSON><PERSON>（推）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:39:04", "updated_at": "2024-12-06 18:39:04"}, {"id": 298, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Pull（拉）", "label": "<PERSON><PERSON>（拉）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:39:12", "updated_at": "2024-12-06 18:39:12"}, {"id": 299, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Rotate（旋转）", "label": "Rotate（旋转）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:39:21", "updated_at": "2024-12-06 18:39:21"}, {"id": 300, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Lift（抬起）", "label": "Lift（抬起）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:39:32", "updated_at": "2024-12-06 18:39:32"}, {"id": 301, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Lower（放下）", "label": "Lower（放下）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:39:41", "updated_at": "2024-12-06 18:39:41"}, {"id": 302, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Press（按压）", "label": "Press（按压）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:39:48", "updated_at": "2024-12-06 18:39:48"}, {"id": 303, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Swipe（滑动）", "label": "Swipe（滑动）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:39:55", "updated_at": "2024-12-06 18:39:55"}, {"id": 304, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Tap（轻敲）", "label": "<PERSON><PERSON>（轻敲）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:40:02", "updated_at": "2024-12-06 18:40:02"}, {"id": 305, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Point（指向）", "label": "Point（指向）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:40:35", "updated_at": "2024-12-06 18:40:35"}, {"id": 306, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Flip（翻转）", "label": "Flip（翻转）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:41:22", "updated_at": "2024-12-06 18:41:22"}, {"id": 307, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Slide（滑动）", "label": "Slide（滑动）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:41:31", "updated_at": "2024-12-06 18:41:31"}, {"id": 308, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Pick（拾起）", "label": "Pick（拿起）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:41:40", "updated_at": "2024-12-08 02:50:38"}, {"id": 309, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Place（放置）", "label": "Place（放置）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:41:50", "updated_at": "2024-12-06 18:41:50"}, {"id": 310, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Shake（摇动）", "label": "Shake（摇动）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:41:57", "updated_at": "2024-12-06 18:41:57"}, {"id": 311, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Open（打开）", "label": "Open（打开）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:49:01", "updated_at": "2024-12-06 18:49:01"}, {"id": 312, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Close（关闭）", "label": "Close（关闭）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:49:15", "updated_at": "2024-12-06 18:49:15"}, {"id": 313, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Turn（转动）", "label": "Turn（转动）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:49:37", "updated_at": "2024-12-06 18:49:37"}, {"id": 314, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Scratch（刮擦）", "label": "<PERSON><PERSON><PERSON>（刮擦）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:49:44", "updated_at": "2024-12-06 18:49:44"}, {"id": 315, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Pinch（捏）", "label": "<PERSON><PERSON>（捏）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:49:54", "updated_at": "2024-12-06 18:49:54"}, {"id": 316, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Hold（握住）", "label": "Hold（握住）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:50:03", "updated_at": "2024-12-06 18:50:03"}, {"id": 317, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Drop（丢下）", "label": "Drop（丢下）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:50:12", "updated_at": "2024-12-06 18:50:12"}, {"id": 318, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Roll（滚动）", "label": "Roll（滚动）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:50:20", "updated_at": "2024-12-06 18:50:20"}, {"id": 319, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Wave（挥手）", "label": "Wave（挥手）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:50:27", "updated_at": "2024-12-06 18:50:27"}, {"id": 320, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Catch（接住）", "label": "Catch（接住）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:50:34", "updated_at": "2024-12-06 18:50:34"}, {"id": 321, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Insert（插入）", "label": "Insert（插入）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:50:46", "updated_at": "2024-12-06 18:50:46"}, {"id": 322, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Remove（移除）", "label": "Remove（移除）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:50:54", "updated_at": "2024-12-06 18:50:54"}, {"id": 323, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Stretch（伸展）", "label": "<PERSON><PERSON><PERSON>（伸展）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:51:03", "updated_at": "2024-12-06 18:51:03"}, {"id": 324, "tenant_id": 1, "code": "task_atomic_ability_single_arm_PressButton（按按钮）", "label": "PressButton（按按钮）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:51:13", "updated_at": "2024-12-06 18:51:13"}, {"id": 325, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Transport（搬运）", "label": "Transport（搬运）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:51:23", "updated_at": "2024-12-06 18:51:23"}, {"id": 326, "tenant_id": 1, "code": "task_atomic_ability_single_arm_PullApart（拉开）", "label": "<PERSON>ull<PERSON><PERSON><PERSON>（拉开）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:51:31", "updated_at": "2024-12-06 18:51:31"}, {"id": 327, "tenant_id": 1, "code": "task_atomic_ability_single_arm_PushTogether（推合）", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>（推合）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:51:39", "updated_at": "2024-12-06 18:51:39"}, {"id": 328, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Twist（扭转）", "label": "Twist（扭转）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:51:49", "updated_at": "2024-12-06 18:51:49"}, {"id": 329, "tenant_id": 1, "code": "task_atomic_ability_single_arm_OpenJar（开罐）", "label": "OpenJar（开罐）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:51:57", "updated_at": "2024-12-06 18:51:57"}, {"id": 330, "tenant_id": 1, "code": "task_atomic_ability_single_arm_CloseJar（关罐）", "label": "<PERSON><PERSON><PERSON>（关罐）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:53:25", "updated_at": "2024-12-06 18:53:25"}, {"id": 331, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Fold（折叠）", "label": "Fold（折叠）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:53:35", "updated_at": "2024-12-06 18:53:35"}, {"id": 332, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Unfold（展开）", "label": "Unfold（展开）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:53:44", "updated_at": "2024-12-06 18:53:44"}, {"id": 333, "tenant_id": 1, "code": "task_atomic_ability_single_arm_LiftHeavyObject（举重）", "label": "LiftHeavyObject（举重）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:53:52", "updated_at": "2024-12-06 18:53:52"}, {"id": 334, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Carry（携带）", "label": "Carry（携带）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:54:00", "updated_at": "2024-12-06 18:54:00"}, {"id": 335, "tenant_id": 1, "code": "task_atomic_ability_single_arm_TurnWheel（转动轮子）", "label": "TurnWheel（转动轮子）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:54:08", "updated_at": "2024-12-06 18:54:08"}, {"id": 336, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Clap（拍手）", "label": "Clap（拍手）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:54:17", "updated_at": "2024-12-06 18:54:17"}, {"id": 337, "tenant_id": 1, "code": "task_atomic_ability_single_arm_OpenBox（打开盒子）", "label": "OpenBox（打开盒子）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:54:28", "updated_at": "2024-12-06 18:54:28"}, {"id": 338, "tenant_id": 1, "code": "task_atomic_ability_single_arm_CloseBox（关闭盒子）", "label": "CloseBox（关闭盒子）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:54:38", "updated_at": "2024-12-06 18:54:38"}, {"id": 339, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Tie（系紧）", "label": "<PERSON><PERSON>（系紧）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:54:47", "updated_at": "2024-12-06 18:54:47"}, {"id": 340, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Untie（解开）", "label": "<PERSON><PERSON>（解开）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:54:54", "updated_at": "2024-12-06 18:54:54"}, {"id": 341, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Stack（堆叠）", "label": "Stack（堆叠）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:55:01", "updated_at": "2024-12-06 18:55:01"}, {"id": 342, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Unstack（拆卸）", "label": "Unstack（拆卸）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:55:09", "updated_at": "2024-12-06 18:55:09"}, {"id": 343, "tenant_id": 1, "code": "task_atomic_ability_single_arm_HoldLargeObject（握持大物体）", "label": "HoldLargeObject（握持大物体）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:55:18", "updated_at": "2024-12-06 18:55:18"}, {"id": 344, "tenant_id": 1, "code": "task_atomic_ability_single_arm_RollDough（擀面）", "label": "RollDough（擀面）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:55:28", "updated_at": "2024-12-06 18:55:28"}, {"id": 345, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Knead（揉捏）", "label": "<PERSON><PERSON><PERSON>（揉捏）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:55:36", "updated_at": "2024-12-06 18:55:36"}, {"id": 346, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Wipe（擦拭）", "label": "<PERSON><PERSON><PERSON>（擦拭）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:55:46", "updated_at": "2024-12-06 18:55:46"}, {"id": 347, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Cut（切割）", "label": "Cut（切割）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 18:55:54", "updated_at": "2024-12-06 18:55:54"}, {"id": 348, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Beat（敲打）", "label": "Beat（敲打）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:00:03", "updated_at": "2024-12-06 19:00:03"}, {"id": 349, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Hammer（锤击）", "label": "<PERSON>（锤击）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:00:11", "updated_at": "2024-12-06 19:00:11"}, {"id": 350, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Screw（拧紧）", "label": "<PERSON><PERSON>（拧紧）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:00:19", "updated_at": "2025-02-10 11:43:47"}, {"id": 351, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Unscrew（拧松螺丝）", "label": "Unscrew（拧松螺丝）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:00:28", "updated_at": "2024-12-06 19:00:28"}, {"id": 352, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Paint（涂刷）", "label": "Paint（涂刷）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:00:37", "updated_at": "2024-12-06 19:00:37"}, {"id": 353, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Sweep（扫地）", "label": "Sweep（扫地）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:00:50", "updated_at": "2024-12-06 19:00:50"}, {"id": 354, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Mop（拖地）", "label": "<PERSON><PERSON>（拖地）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:00:57", "updated_at": "2024-12-06 19:00:57"}, {"id": 355, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Drill（钻孔）", "label": "Drill（钻孔）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:01:04", "updated_at": "2024-12-06 19:01:04"}, {"id": 356, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Saw（锯）", "label": "<PERSON>（锯）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:01:11", "updated_at": "2024-12-06 19:01:11"}, {"id": 357, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Stir（搅拌）", "label": "Stir（搅拌）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:01:20", "updated_at": "2024-12-06 19:01:20"}, {"id": 358, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Chop（剁碎）", "label": "<PERSON><PERSON>（剁碎）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:01:27", "updated_at": "2024-12-06 19:01:27"}, {"id": 359, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Whisk（搅打）", "label": "Whisk（搅打）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:01:37", "updated_at": "2024-12-06 19:01:37"}, {"id": 360, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Scoop(舀)", "label": "<PERSON><PERSON>(舀)", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:01:47", "updated_at": "2024-12-06 19:01:47"}, {"id": 361, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Peel（削皮）", "label": "Peel（削皮）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:01:55", "updated_at": "2024-12-06 19:01:55"}, {"id": 362, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Brush（刷）", "label": "Brush（刷）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:02:03", "updated_at": "2024-12-06 19:02:03"}, {"id": 363, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Hang（挂）", "label": "<PERSON>（挂）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-06 19:02:12", "updated_at": "2024-12-06 19:02:12"}, {"id": 364, "tenant_id": 1, "code": "task_resource_kind_家具", "label": "家具", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 19:09:49", "updated_at": "2024-12-06 19:09:49"}, {"id": 365, "tenant_id": 1, "code": "task_resource_region_A户型", "label": "A户型", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-06 19:10:44", "updated_at": "2024-12-06 19:18:52"}, {"id": 366, "tenant_id": 1, "code": "task_resource_region_B户型", "label": "B户型", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-06 19:11:08", "updated_at": "2024-12-06 19:18:59"}, {"id": 367, "tenant_id": 1, "code": "task_resource_region_C户型", "label": "C户型", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-06 19:11:52", "updated_at": "2024-12-06 19:19:07"}, {"id": 368, "tenant_id": 1, "code": "task_resource_region_D户型", "label": "D户型", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-06 19:11:57", "updated_at": "2024-12-06 19:19:14"}, {"id": 369, "tenant_id": 1, "code": "task_resource_kind_目标盘", "label": "目标盘", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 20:59:52", "updated_at": "2024-12-06 20:59:52"}, {"id": 370, "tenant_id": 1, "code": "task_resource_kind_服装", "label": "服装", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 21:36:55", "updated_at": "2024-12-06 21:36:55"}, {"id": 371, "tenant_id": 1, "code": "task_resource_kind_户型", "label": "户型", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 22:05:38", "updated_at": "2024-12-06 22:05:38"}, {"id": 372, "tenant_id": 1, "code": "task_resource_kind_其他", "label": "其他", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-06 22:26:57", "updated_at": "2024-12-06 22:26:57"}, {"id": 373, "tenant_id": 1, "code": "task_resource_region_其他", "label": "其他", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-06 22:27:02", "updated_at": "2024-12-06 22:27:02"}, {"id": 374, "tenant_id": 1, "code": "task_resource_kind_中转箱", "label": "中转箱", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 11:34:39", "updated_at": "2024-12-07 11:34:39"}, {"id": 375, "tenant_id": 1, "code": "task_resource_kind_超市货架", "label": "超市货架", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 11:34:56", "updated_at": "2024-12-07 11:34:56"}, {"id": 376, "tenant_id": 1, "code": "task_resource_kind_购物袋", "label": "购物袋", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 13:39:52", "updated_at": "2024-12-07 13:39:52"}, {"id": 377, "tenant_id": 1, "code": "task_resource_kind_洗漱用品", "label": "洗漱用品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 15:19:36", "updated_at": "2024-12-07 15:19:36"}, {"id": 378, "tenant_id": 1, "code": "task_resource_kind_护肤品", "label": "护肤品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 15:19:42", "updated_at": "2024-12-07 15:19:42"}, {"id": 379, "tenant_id": 1, "code": "task_resource_region_浴室", "label": "浴室", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-07 15:21:04", "updated_at": "2024-12-07 15:21:04"}, {"id": 380, "tenant_id": 1, "code": "task_resource_kind_化妆品", "label": "化妆品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 15:38:40", "updated_at": "2024-12-07 15:38:40"}, {"id": 381, "tenant_id": 1, "code": "task_resource_region_厨房", "label": "厨房", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-07 15:47:32", "updated_at": "2024-12-07 15:47:32"}, {"id": 382, "tenant_id": 1, "code": "task_resource_region_客厅", "label": "客厅", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-07 15:47:39", "updated_at": "2024-12-07 15:47:39"}, {"id": 383, "tenant_id": 1, "code": "task_resource_kind_厨具", "label": "厨具", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 15:49:54", "updated_at": "2024-12-07 15:49:54"}, {"id": 384, "tenant_id": 1, "code": "task_resource_kind_瓶装饮料", "label": "瓶装饮料", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 16:18:47", "updated_at": "2024-12-07 16:18:47"}, {"id": 385, "tenant_id": 1, "code": "task_resource_kind_纸盒饮料", "label": "纸盒饮料", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 16:19:00", "updated_at": "2024-12-07 16:19:00"}, {"id": 386, "tenant_id": 1, "code": "task_resource_kind_袋装冷藏食品", "label": "袋装冷藏食品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 16:20:09", "updated_at": "2024-12-07 16:20:09"}, {"id": 387, "tenant_id": 1, "code": "task_resource_kind_厨房调味瓶", "label": "厨房调味瓶", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 16:20:17", "updated_at": "2024-12-07 16:20:17"}, {"id": 388, "tenant_id": 1, "code": "task_resource_kind_厨房清洁物品", "label": "厨房清洁物品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 16:20:32", "updated_at": "2024-12-07 16:20:32"}, {"id": 389, "tenant_id": 1, "code": "task_resource_kind_厨房情节物品", "label": "厨房情节物品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-07 16:29:06", "updated_at": "2024-12-07 16:29:06"}, {"id": 390, "tenant_id": 1, "code": "task_resource_scene_烹饪", "label": "烹饪", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-07 19:21:14", "updated_at": "2024-12-07 19:21:14"}, {"id": 391, "tenant_id": 1, "code": "task_scene_classification_烹饪", "label": "烹饪", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-07 19:21:14", "updated_at": "2024-12-07 19:21:14"}, {"id": 392, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Pour（倒）", "label": "Pour（倒）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-07 19:35:09", "updated_at": "2024-12-07 19:35:09"}, {"id": 393, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Move（移动）", "label": "Move（移动）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-09 14:48:29", "updated_at": "2024-12-09 14:48:29"}, {"id": 394, "tenant_id": 1, "code": "task_atomic_ability_single_arm_HandOver（传递）", "label": "HandOver（传递）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-09 14:48:47", "updated_at": "2024-12-09 14:48:47"}, {"id": 395, "tenant_id": 1, "code": "task_resource_kind_电子产品", "label": "电子产品", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-11 16:43:55", "updated_at": "2024-12-11 16:43:55"}, {"id": 396, "tenant_id": 1, "code": "task_atomic_ability_single_arm_dip（浸）", "label": "dip（浸）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-15 15:14:48", "updated_at": "2024-12-15 15:14:48"}, {"id": 397, "tenant_id": 1, "code": "task_atomic_ability_single_arm_HandOut（递给）", "label": "HandOut（递给）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-15 15:53:19", "updated_at": "2024-12-15 15:53:19"}, {"id": 398, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Iron（熨）", "label": "Iron（熨）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-15 16:05:08", "updated_at": "2024-12-15 16:05:08"}, {"id": 399, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Scan（扫描）", "label": "<PERSON><PERSON>（扫描）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-16 18:55:41", "updated_at": "2024-12-16 18:55:41"}, {"id": 400, "tenant_id": 1, "code": "task_resource_region_餐厅", "label": "餐厅", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-17 16:55:01", "updated_at": "2024-12-17 16:55:01"}, {"id": 401, "tenant_id": 1, "code": "task_resource_region_超市", "label": "超市", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-17 16:55:09", "updated_at": "2024-12-17 16:55:09"}, {"id": 402, "tenant_id": 1, "code": "task_resource_region_奶茶店", "label": "奶茶店", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-17 16:55:36", "updated_at": "2024-12-17 16:55:36"}, {"id": 403, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Straighten（整理）", "label": "<PERSON>en（整理）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-23 16:39:59", "updated_at": "2024-12-23 16:39:59"}, {"id": 404, "tenant_id": 1, "code": "task_resource_scene_Household", "label": "Household", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-25 10:02:50", "updated_at": "2024-12-25 10:02:50"}, {"id": 405, "tenant_id": 1, "code": "task_scene_classification_Household", "label": "Household", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-25 10:02:50", "updated_at": "2024-12-25 10:02:50"}, {"id": 406, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Takeout（取出）", "label": "Takeout（取出）", "pid": 260, "level": 3, "status": 2, "created_at": "2024-12-28 19:13:43", "updated_at": "2024-12-28 19:13:43"}, {"id": 407, "tenant_id": 1, "code": "task_resource_region_办公室", "label": "办公室", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-30 14:32:43", "updated_at": "2024-12-30 14:32:43"}, {"id": 408, "tenant_id": 1, "code": "task_resource_scene_会议室", "label": "会议室", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-30 14:34:05", "updated_at": "2024-12-30 14:34:05"}, {"id": 409, "tenant_id": 1, "code": "task_scene_classification_会议室", "label": "会议室", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-30 14:34:05", "updated_at": "2024-12-30 14:34:05"}, {"id": 410, "tenant_id": 1, "code": "task_resource_scene_办公区", "label": "办公区", "pid": 242, "level": 3, "status": 1, "created_at": "2024-12-30 14:34:16", "updated_at": "2024-12-30 14:34:16"}, {"id": 411, "tenant_id": 1, "code": "task_scene_classification_办公区", "label": "办公区", "pid": 246, "level": 3, "status": 2, "created_at": "2024-12-30 14:34:16", "updated_at": "2024-12-30 14:34:16"}, {"id": 412, "tenant_id": 1, "code": "task_resource_region_会议室", "label": "会议室", "pid": 244, "level": 3, "status": 2, "created_at": "2024-12-30 16:51:30", "updated_at": "2024-12-30 16:51:30"}, {"id": 413, "tenant_id": 1, "code": "task_resource_kind_场景", "label": "场景", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-30 16:52:11", "updated_at": "2024-12-30 16:52:11"}, {"id": 414, "tenant_id": 1, "code": "task_resource_kind_幼儿玩具", "label": "幼儿玩具", "pid": 243, "level": 3, "status": 2, "created_at": "2024-12-31 11:27:58", "updated_at": "2024-12-31 11:27:58"}, {"id": 415, "tenant_id": 1, "code": "task_resource_kind_药品", "label": "药品", "pid": 243, "level": 3, "status": 2, "created_at": "2025-01-03 09:38:49", "updated_at": "2025-01-03 09:38:49"}, {"id": 416, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Rinse（冲洗）", "label": "Rinse（冲洗）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-01-07 16:03:31", "updated_at": "2025-01-07 16:03:31"}, {"id": 417, "tenant_id": 1, "code": "task_atomic_ability_single_arm_squeeze（挤）", "label": "squeeze（挤）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-01-07 16:03:42", "updated_at": "2025-01-07 16:03:42"}, {"id": 418, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Unzip（拉开拉链）", "label": "Unzip（拉开拉链）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-01-10 09:55:27", "updated_at": "2025-01-10 09:55:27"}, {"id": 419, "tenant_id": 1, "code": "task_atomic_ability_single_arm_water（浇水）", "label": "water（浇水）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-01-14 15:23:20", "updated_at": "2025-01-14 15:23:20"}, {"id": 420, "tenant_id": 1, "code": "task_atomic_ability_single_arm_armretract（手臂内收）", "label": "arm<PERSON><PERSON><PERSON>（手臂内收）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-01-14 15:23:27", "updated_at": "2025-01-14 15:23:27"}, {"id": 421, "tenant_id": 1, "code": "task_atomic_ability_single_arm_TakeOver（接过）", "label": "TakeOver（接过）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-01-14 18:56:18", "updated_at": "2025-01-14 18:57:11"}, {"id": 422, "tenant_id": 1, "code": "task_default_review_label", "label": "默认审核标签", "pid": 1, "level": 2, "status": 4, "created_at": "2025-01-18 10:35:31", "updated_at": "2025-01-18 10:35:31"}, {"id": 424, "tenant_id": 1, "code": "task_end_kind_z<PERSON><PERSON>_gripper_omnipicker", "label": "<PERSON><PERSON><PERSON>_gripper_omnipicker", "pid": 248, "level": 3, "status": 2, "created_at": "2025-02-05 15:28:34", "updated_at": "2025-02-05 15:36:47"}, {"id": 425, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Uncap（拔）", "label": "Uncap（拔）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-02-06 20:12:54", "updated_at": "2025-02-06 20:12:54"}, {"id": 426, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Unplug (拔插头)", "label": "Unplug (拔插头)", "pid": 260, "level": 3, "status": 2, "created_at": "2025-02-07 17:19:53", "updated_at": "2025-02-07 17:19:53"}, {"id": 427, "tenant_id": 1, "code": "task_atomic_ability_single_arm_plug (插插头)", "label": "plug (插插头)", "pid": 260, "level": 3, "status": 2, "created_at": "2025-02-07 17:20:09", "updated_at": "2025-02-07 17:20:09"}, {"id": 430, "tenant_id": 1, "code": "task_atomic_ability_single_arm_stamp（盖章）", "label": "stamp（盖章）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-02-14 18:56:36", "updated_at": "2025-02-14 18:56:59"}, {"id": 431, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Stick（粘贴）", "label": "Stick（粘贴）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-02-17 14:16:07", "updated_at": "2025-02-17 14:16:07"}, {"id": 432, "tenant_id": 1, "code": "task_resource_scene_办公", "label": "办公", "pid": 242, "level": 3, "status": 1, "created_at": "2025-02-18 16:07:32", "updated_at": "2025-02-18 16:07:32"}, {"id": 433, "tenant_id": 1, "code": "task_scene_classification_办公", "label": "办公", "pid": 246, "level": 3, "status": 2, "created_at": "2025-02-18 16:07:32", "updated_at": "2025-02-18 16:07:32"}, {"id": 434, "tenant_id": 1, "code": "task_resource_scene_卧室", "label": "卧室", "pid": 242, "level": 3, "status": 1, "created_at": "2025-02-18 18:21:03", "updated_at": "2025-02-18 18:21:03"}, {"id": 435, "tenant_id": 1, "code": "task_scene_classification_卧室", "label": "卧室", "pid": 246, "level": 3, "status": 2, "created_at": "2025-02-18 18:21:03", "updated_at": "2025-02-18 18:21:03"}, {"id": 436, "tenant_id": 1, "code": "task_resource_scene_工业", "label": "工业", "pid": 242, "level": 3, "status": 1, "created_at": "2025-02-18 18:21:20", "updated_at": "2025-02-18 18:21:20"}, {"id": 437, "tenant_id": 1, "code": "task_scene_classification_工业", "label": "工业", "pid": 246, "level": 3, "status": 2, "created_at": "2025-02-18 18:21:20", "updated_at": "2025-02-18 18:21:20"}, {"id": 438, "tenant_id": 1, "code": "task_resource_scene_商超", "label": "商超", "pid": 242, "level": 3, "status": 1, "created_at": "2025-02-18 18:21:31", "updated_at": "2025-02-18 18:21:31"}, {"id": 439, "tenant_id": 1, "code": "task_scene_classification_商超", "label": "商超", "pid": 246, "level": 3, "status": 2, "created_at": "2025-02-18 18:21:31", "updated_at": "2025-02-18 18:21:31"}, {"id": 440, "tenant_id": 1, "code": "task_resource_scene_办公室", "label": "办公室", "pid": 242, "level": 3, "status": 1, "created_at": "2025-02-18 18:21:46", "updated_at": "2025-02-18 18:21:46"}, {"id": 441, "tenant_id": 1, "code": "task_scene_classification_办公室", "label": "办公室", "pid": 246, "level": 3, "status": 2, "created_at": "2025-02-18 18:21:46", "updated_at": "2025-02-18 18:21:46"}, {"id": 442, "tenant_id": 1, "code": "task_resource_scene_餐饮", "label": "餐饮", "pid": 242, "level": 3, "status": 1, "created_at": "2025-02-18 18:21:54", "updated_at": "2025-02-18 18:21:54"}, {"id": 443, "tenant_id": 1, "code": "task_scene_classification_餐饮", "label": "餐饮", "pid": 246, "level": 3, "status": 2, "created_at": "2025-02-18 18:21:54", "updated_at": "2025-02-18 18:21:54"}, {"id": 444, "tenant_id": 1, "code": "task_resource_scene_书房", "label": "书房", "pid": 242, "level": 3, "status": 1, "created_at": "2025-02-18 21:44:08", "updated_at": "2025-02-18 21:44:08"}, {"id": 445, "tenant_id": 1, "code": "task_scene_classification_书房", "label": "书房", "pid": 246, "level": 3, "status": 2, "created_at": "2025-02-18 21:44:08", "updated_at": "2025-02-18 21:44:08"}, {"id": 446, "tenant_id": 1, "code": "task_end_kind_zhi<PERSON>_finger_SkillHandS6", "label": "zhi<PERSON>_finger_SkillHandS6", "pid": 248, "level": 3, "status": 2, "created_at": "2025-03-07 11:03:33", "updated_at": "2025-03-07 11:03:33"}, {"id": 448, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Other（其他）", "label": "Other（其他）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-03-13 10:16:13", "updated_at": "2025-03-13 10:16:13"}, {"id": 449, "tenant_id": 1, "code": "task_project", "label": "所属项目", "pid": 1, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:42", "updated_at": "2025-03-14 04:01:42"}, {"id": 450, "tenant_id": 1, "code": "task_camera_rgbd", "label": "相机(rgbd)", "pid": 1, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:42", "updated_at": "2025-03-14 04:01:42"}, {"id": 451, "tenant_id": 1, "code": "task_camera_rgbd_head", "label": "head", "pid": 450, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:42", "updated_at": "2025-03-14 04:01:42"}, {"id": 452, "tenant_id": 1, "code": "task_camera_rgbd_hand_left", "label": "hand_left", "pid": 450, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:42", "updated_at": "2025-03-14 04:01:42"}, {"id": 453, "tenant_id": 1, "code": "task_camera_rgbd_hand_right", "label": "hand_right", "pid": 450, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:42", "updated_at": "2025-03-14 04:01:42"}, {"id": 454, "tenant_id": 1, "code": "task_camera_fisheye", "label": "相机(fisheye)", "pid": 1, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:42", "updated_at": "2025-03-14 04:01:42"}, {"id": 455, "tenant_id": 1, "code": "task_camera_fisheye_head_center_fisheye", "label": "head_center_fisheye", "pid": 454, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:42", "updated_at": "2025-03-14 04:01:42"}, {"id": 456, "tenant_id": 1, "code": "task_camera_fisheye_head_left_fisheye", "label": "head_left_fisheye", "pid": 454, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:43", "updated_at": "2025-03-14 04:01:43"}, {"id": 457, "tenant_id": 1, "code": "task_camera_fisheye_head_right_fisheye", "label": "head_right_fisheye", "pid": 454, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:43", "updated_at": "2025-03-14 04:01:43"}, {"id": 458, "tenant_id": 1, "code": "task_camera_fisheye_hand_left_fisheye", "label": "hand_left_fisheye", "pid": 454, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:43", "updated_at": "2025-03-14 04:01:43"}, {"id": 459, "tenant_id": 1, "code": "task_camera_fisheye_hand_right_fisheye", "label": "hand_right_fisheye", "pid": 454, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:43", "updated_at": "2025-03-14 04:01:43"}, {"id": 460, "tenant_id": 1, "code": "task_camera_fisheye_back_left_fisheye", "label": "back_left_fisheye", "pid": 454, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:43", "updated_at": "2025-03-14 04:01:43"}, {"id": 461, "tenant_id": 1, "code": "task_camera_fisheye_back_right_fisheye", "label": "back_right_fisheye", "pid": 454, "level": 2, "status": 4, "created_at": "2025-03-14 04:01:43", "updated_at": "2025-03-14 04:01:43"}, {"id": 462, "tenant_id": 1, "code": "task_project_PI", "label": "PI", "pid": 449, "level": 3, "status": 2, "created_at": "2025-03-14 12:24:10", "updated_at": "2025-03-14 12:24:10"}, {"id": 463, "tenant_id": 1, "code": "task_project_test", "label": "test", "pid": 449, "level": 3, "status": 2, "created_at": "2025-03-14 12:24:21", "updated_at": "2025-03-14 12:24:21"}, {"id": 467, "tenant_id": 1, "code": "task_project_算法专项", "label": "算法专项", "pid": 449, "level": 3, "status": 2, "created_at": "2025-03-18 14:31:39", "updated_at": "2025-03-18 14:31:39"}, {"id": 468, "tenant_id": 1, "code": "task_project_常规", "label": "常规", "pid": 449, "level": 3, "status": 2, "created_at": "2025-03-18 14:31:46", "updated_at": "2025-03-18 14:31:46"}, {"id": 469, "tenant_id": 1, "code": "task_project_常规-全身", "label": "常规-全身", "pid": 449, "level": 3, "status": 2, "created_at": "2025-03-18 14:31:55", "updated_at": "2025-03-18 14:31:55"}, {"id": 470, "tenant_id": 1, "code": "task_project_327专项", "label": "327专项", "pid": 449, "level": 3, "status": 2, "created_at": "2025-03-20 10:07:44", "updated_at": "2025-03-20 10:07:44"}, {"id": 472, "tenant_id": 1, "code": "task_project_AGI", "label": "AGI", "pid": 449, "level": 3, "status": 2, "created_at": "2025-03-20 11:15:53", "updated_at": "2025-03-20 11:15:53"}, {"id": 473, "tenant_id": 1, "code": "task_project_算法专用", "label": "算法专用", "pid": 449, "level": 3, "status": 2, "created_at": "2025-03-26 13:36:32", "updated_at": "2025-03-26 13:36:32"}, {"id": 474, "tenant_id": 1, "code": "task_project_HW-poc", "label": "HW-poc", "pid": 449, "level": 3, "status": 2, "created_at": "2025-03-27 17:15:10", "updated_at": "2025-03-27 17:15:10"}, {"id": 489, "tenant_id": 1, "code": "task_atomic_ability_single_arm_PullOut（拔出）", "label": "Pull<PERSON>ut（拔出）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-04-08 18:37:45", "updated_at": "2025-04-08 18:39:32"}, {"id": 490, "tenant_id": 2, "code": "task", "label": "任务标签", "pid": 0, "level": 1, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 491, "tenant_id": 2, "code": "task_stage", "label": "任务用途", "pid": 490, "level": 2, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 492, "tenant_id": 2, "code": "task_stage_prod", "label": "正式采集", "pid": 491, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 493, "tenant_id": 2, "code": "task_stage_test", "label": "开发测试", "pid": 491, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 494, "tenant_id": 2, "code": "task_stage_pilot_prod", "label": "运营试采", "pid": 491, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 495, "tenant_id": 2, "code": "task_stage_simulation", "label": "仿真评测", "pid": 491, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 496, "tenant_id": 2, "code": "task_scene_classification", "label": "场景分类", "pid": 490, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 497, "tenant_id": 2, "code": "task_scene_classification_工厂", "label": "工厂", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 498, "tenant_id": 2, "code": "task_scene_classification_家居", "label": "家居", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 499, "tenant_id": 2, "code": "task_scene_classification_超市", "label": "超市", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 500, "tenant_id": 2, "code": "task_scene_classification_咖啡厅", "label": "咖啡厅", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 501, "tenant_id": 2, "code": "task_scene_classification_餐厅", "label": "餐厅", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 502, "tenant_id": 2, "code": "task_scene_classification_厨房", "label": "厨房", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 503, "tenant_id": 2, "code": "task_scene_classification_奶茶店", "label": "奶茶店", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 504, "tenant_id": 2, "code": "task_scene_classification_浴室", "label": "浴室", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 505, "tenant_id": 2, "code": "task_scene_classification_客厅", "label": "客厅", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 506, "tenant_id": 2, "code": "task_scene_classification_烹饪", "label": "烹饪", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 507, "tenant_id": 2, "code": "task_scene_classification_Household", "label": "Household", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 508, "tenant_id": 2, "code": "task_scene_classification_会议室", "label": "会议室", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 509, "tenant_id": 2, "code": "task_scene_classification_办公区", "label": "办公区", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 510, "tenant_id": 2, "code": "task_scene_classification_办公", "label": "办公", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 511, "tenant_id": 2, "code": "task_scene_classification_卧室", "label": "卧室", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 512, "tenant_id": 2, "code": "task_scene_classification_工业", "label": "工业", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 513, "tenant_id": 2, "code": "task_scene_classification_商超", "label": "商超", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 514, "tenant_id": 2, "code": "task_scene_classification_办公室", "label": "办公室", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 515, "tenant_id": 2, "code": "task_scene_classification_餐饮", "label": "餐饮", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 516, "tenant_id": 2, "code": "task_scene_classification_书房", "label": "书房", "pid": 496, "level": 3, "status": 2, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 517, "tenant_id": 2, "code": "task_collect_ontology", "label": "采集本体", "pid": 490, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 518, "tenant_id": 2, "code": "task_end_kind", "label": "末端类型", "pid": 490, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 519, "tenant_id": 2, "code": "task_remote_control_kind", "label": "遥操类型", "pid": 490, "level": 2, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 520, "tenant_id": 2, "code": "task_remote_control_kind_vr", "label": "VR", "pid": 519, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 521, "tenant_id": 2, "code": "task_remote_control_kind_motion_capture", "label": "动捕", "pid": 519, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 522, "tenant_id": 2, "code": "task_collect_mode", "label": "采集模式", "pid": 490, "level": 2, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 523, "tenant_id": 2, "code": "task_collect_mode_full_body", "label": "全身", "pid": 522, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 524, "tenant_id": 2, "code": "task_collect_mode_both_arms", "label": "双臂", "pid": 522, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 525, "tenant_id": 2, "code": "task_collect_mode_left_arm", "label": "单臂_左", "pid": 522, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 526, "tenant_id": 2, "code": "task_collect_mode_right_arm", "label": "单臂_右", "pid": 522, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 527, "tenant_id": 2, "code": "sensor_type", "label": "传感器类型", "pid": 490, "level": 2, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 528, "tenant_id": 2, "code": "5*fisheye+3*rgbd", "label": "5*fisheye+3*rgbd", "pid": 527, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 529, "tenant_id": 2, "code": "7*fisheye+1*rgbd", "label": "7*fisheye+1*rgbd", "pid": 527, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 530, "tenant_id": 2, "code": "task_atomic_ability_single_arm", "label": "原子能力(通用)", "pid": 490, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 531, "tenant_id": 2, "code": "task_atomic_ability_both_arms", "label": "原子能力(其他)", "pid": 490, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 532, "tenant_id": 2, "code": "task_default_review_label", "label": "默认审核标签", "pid": 490, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 533, "tenant_id": 2, "code": "task_project", "label": "所属项目", "pid": 490, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 534, "tenant_id": 2, "code": "task_camera_rgbd", "label": "相机(rgbd)", "pid": 490, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 535, "tenant_id": 2, "code": "task_camera_rgbd_head", "label": "head", "pid": 534, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 536, "tenant_id": 2, "code": "task_camera_rgbd_hand_left", "label": "hand_left", "pid": 534, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 537, "tenant_id": 2, "code": "task_camera_rgbd_hand_right", "label": "hand_right", "pid": 534, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 538, "tenant_id": 2, "code": "task_camera_fisheye", "label": "相机(fisheye)", "pid": 490, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 539, "tenant_id": 2, "code": "task_camera_fisheye_head_center_fisheye", "label": "head_center_fisheye", "pid": 538, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 540, "tenant_id": 2, "code": "task_camera_fisheye_head_left_fisheye", "label": "head_left_fisheye", "pid": 538, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 541, "tenant_id": 2, "code": "task_camera_fisheye_head_right_fisheye", "label": "head_right_fisheye", "pid": 538, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 542, "tenant_id": 2, "code": "task_camera_fisheye_hand_left_fisheye", "label": "hand_left_fisheye", "pid": 538, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 543, "tenant_id": 2, "code": "task_camera_fisheye_hand_right_fisheye", "label": "hand_right_fisheye", "pid": 538, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 544, "tenant_id": 2, "code": "task_camera_fisheye_back_left_fisheye", "label": "back_left_fisheye", "pid": 538, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 545, "tenant_id": 2, "code": "task_camera_fisheye_back_right_fisheye", "label": "back_right_fisheye", "pid": 538, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 546, "tenant_id": 2, "code": "task_resource", "label": "物体标签", "pid": 0, "level": 1, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 547, "tenant_id": 2, "code": "task_resource_scene", "label": "所属场景", "pid": 546, "level": 2, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 548, "tenant_id": 2, "code": "task_resource_scene_工厂", "label": "工厂", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 549, "tenant_id": 2, "code": "task_resource_scene_家居", "label": "家居", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 550, "tenant_id": 2, "code": "task_resource_scene_超市", "label": "超市", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 551, "tenant_id": 2, "code": "task_resource_scene_咖啡厅", "label": "咖啡厅", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 552, "tenant_id": 2, "code": "task_resource_scene_餐厅", "label": "餐厅", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 553, "tenant_id": 2, "code": "task_resource_scene_厨房", "label": "厨房", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 554, "tenant_id": 2, "code": "task_resource_scene_奶茶店", "label": "奶茶店", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 555, "tenant_id": 2, "code": "task_resource_scene_浴室", "label": "浴室", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 556, "tenant_id": 2, "code": "task_resource_scene_客厅", "label": "客厅", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 557, "tenant_id": 2, "code": "task_resource_scene_烹饪", "label": "烹饪", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 558, "tenant_id": 2, "code": "task_resource_scene_Household", "label": "Household", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 559, "tenant_id": 2, "code": "task_resource_scene_会议室", "label": "会议室", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 560, "tenant_id": 2, "code": "task_resource_scene_办公区", "label": "办公区", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 561, "tenant_id": 2, "code": "task_resource_scene_办公", "label": "办公", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 562, "tenant_id": 2, "code": "task_resource_scene_卧室", "label": "卧室", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 563, "tenant_id": 2, "code": "task_resource_scene_工业", "label": "工业", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 564, "tenant_id": 2, "code": "task_resource_scene_商超", "label": "商超", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 565, "tenant_id": 2, "code": "task_resource_scene_办公室", "label": "办公室", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 566, "tenant_id": 2, "code": "task_resource_scene_餐饮", "label": "餐饮", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 567, "tenant_id": 2, "code": "task_resource_scene_书房", "label": "书房", "pid": 547, "level": 3, "status": 1, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 568, "tenant_id": 2, "code": "task_resource_kind", "label": "物体类型", "pid": 546, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 569, "tenant_id": 2, "code": "task_resource_region", "label": "所属区域", "pid": 546, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-09 10:44:36"}, {"id": 570, "tenant_id": 2, "code": "task_resource_feature", "label": "其他特征", "pid": 546, "level": 2, "status": 4, "created_at": "2025-04-09 10:44:36", "updated_at": "2025-04-28 04:04:52"}, {"id": 575, "tenant_id": 1, "code": "task_resource_scene_其他", "label": "其他", "pid": 242, "level": 3, "status": 1, "created_at": "2025-04-10 18:48:46", "updated_at": "2025-04-10 18:48:46"}, {"id": 576, "tenant_id": 1, "code": "task_scene_classification_其他", "label": "其他", "pid": 246, "level": 3, "status": 2, "created_at": "2025-04-10 18:48:46", "updated_at": "2025-04-10 18:48:46"}, {"id": 577, "tenant_id": 1, "code": "task_resource_kind_本体姿态变量", "label": "本体姿态变量", "pid": 243, "level": 3, "status": 2, "created_at": "2025-04-10 18:49:36", "updated_at": "2025-04-10 18:49:36"}, {"id": 578, "tenant_id": 1, "code": "task_resource_kind_位置变量", "label": "位置变量", "pid": 243, "level": 3, "status": 2, "created_at": "2025-04-10 18:49:49", "updated_at": "2025-04-10 18:49:49"}, {"id": 579, "tenant_id": 1, "code": "task_collect_ontology_A2", "label": "A2", "pid": 247, "level": 3, "status": 2, "created_at": "2025-04-14 13:54:16", "updated_at": "2025-04-14 13:54:16"}, {"id": 580, "tenant_id": 1, "code": "task_collect_ontology_X1", "label": "X1", "pid": 247, "level": 3, "status": 2, "created_at": "2025-04-14 13:54:27", "updated_at": "2025-04-14 13:54:27"}, {"id": 581, "tenant_id": 1, "code": "task_project_WAIC", "label": "WAIC", "pid": 449, "level": 3, "status": 2, "created_at": "2025-04-14 14:15:44", "updated_at": "2025-04-14 14:15:44"}, {"id": 583, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Pipette（吸取液体）", "label": "Pipette（吸取液体）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-04-24 11:12:50", "updated_at": "2025-04-24 11:12:50"}, {"id": 584, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Cover（遮盖）", "label": "Cover（遮盖）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-04-24 11:36:34", "updated_at": "2025-04-24 11:36:34"}, {"id": 585, "tenant_id": 1, "code": "task_atomic_ability_single_arm_vacuum（吸尘）", "label": "vacuum（吸尘）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-04-24 13:31:37", "updated_at": "2025-04-24 13:31:37"}, {"id": 586, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Takedown（取下）", "label": "Takedown（取下）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-04-27 09:55:19", "updated_at": "2025-04-27 09:55:28"}, {"id": 587, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Stir-Fry（翻炒）", "label": "Stir-Fry（翻炒）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-04-27 11:35:30", "updated_at": "2025-04-27 11:35:30"}, {"id": 588, "tenant_id": 2, "code": "task_resource_color", "label": "颜色特征", "pid": 546, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 589, "tenant_id": 1, "code": "task_resource_color", "label": "颜色特征", "pid": 241, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 590, "tenant_id": 2, "code": "task_resource_optical", "label": "光学特性", "pid": 546, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 591, "tenant_id": 1, "code": "task_resource_optical", "label": "光学特性", "pid": 241, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 592, "tenant_id": 2, "code": "task_resource_material", "label": "材质构成", "pid": 546, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 593, "tenant_id": 1, "code": "task_resource_material", "label": "材质构成", "pid": 241, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 594, "tenant_id": 2, "code": "task_resource_morphology", "label": "形态特征", "pid": 546, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 595, "tenant_id": 1, "code": "task_resource_morphology", "label": "形态特征", "pid": 241, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 596, "tenant_id": 2, "code": "task_resource_shape", "label": "形状特征", "pid": 546, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 597, "tenant_id": 1, "code": "task_resource_shape", "label": "形状特征", "pid": 241, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 598, "tenant_id": 2, "code": "task_resource_kinematic", "label": "运动特性", "pid": 546, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 599, "tenant_id": 1, "code": "task_resource_kinematic", "label": "运动特性", "pid": 241, "level": 2, "status": 2, "created_at": "2025-04-28 04:04:52", "updated_at": "2025-04-28 04:04:52"}, {"id": 603, "tenant_id": 1, "code": "task_resource_feature_测试", "label": "测试", "pid": 245, "level": 3, "status": 2, "created_at": "2025-04-28 12:07:19", "updated_at": "2025-04-28 12:07:19"}, {"id": 604, "tenant_id": 1, "code": "task_resource_color_蓝色", "label": "蓝色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 12:07:26", "updated_at": "2025-04-28 12:07:26"}, {"id": 605, "tenant_id": 1, "code": "task_resource_optical_透明", "label": "透明", "pid": 591, "level": 3, "status": 2, "created_at": "2025-04-28 12:07:33", "updated_at": "2025-04-28 12:07:33"}, {"id": 606, "tenant_id": 1, "code": "task_resource_material_玻璃", "label": "玻璃", "pid": 593, "level": 3, "status": 2, "created_at": "2025-04-28 12:07:38", "updated_at": "2025-04-28 12:07:38"}, {"id": 607, "tenant_id": 1, "code": "task_resource_material_陶瓷", "label": "陶瓷", "pid": 593, "level": 3, "status": 2, "created_at": "2025-04-28 12:07:43", "updated_at": "2025-04-28 12:07:43"}, {"id": 609, "tenant_id": 1, "code": "task_resource_shape_方形", "label": "方形", "pid": 597, "level": 3, "status": 2, "created_at": "2025-04-28 12:08:12", "updated_at": "2025-04-28 12:08:12"}, {"id": 610, "tenant_id": 1, "code": "task_resource_kinematic_运动", "label": "运动", "pid": 599, "level": 3, "status": 2, "created_at": "2025-04-28 12:09:19", "updated_at": "2025-04-28 12:09:19"}, {"id": 611, "tenant_id": 1, "code": "task_resource_color_灰色", "label": "灰色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:43:23", "updated_at": "2025-04-28 15:43:23"}, {"id": 612, "tenant_id": 1, "code": "task_resource_color_红色", "label": "红色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:44:52", "updated_at": "2025-04-28 15:44:52"}, {"id": 613, "tenant_id": 1, "code": "task_resource_color_橙色", "label": "橙色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:44:57", "updated_at": "2025-04-28 15:44:57"}, {"id": 614, "tenant_id": 1, "code": "task_resource_color_黄色", "label": "黄色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:45:05", "updated_at": "2025-04-28 15:45:05"}, {"id": 615, "tenant_id": 1, "code": "task_resource_color_绿色", "label": "绿色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:45:09", "updated_at": "2025-04-28 15:45:09"}, {"id": 616, "tenant_id": 1, "code": "task_resource_color_紫色", "label": "紫色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:45:24", "updated_at": "2025-04-28 15:45:24"}, {"id": 617, "tenant_id": 1, "code": "task_resource_color_棕色", "label": "棕色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:45:29", "updated_at": "2025-04-28 15:45:29"}, {"id": 618, "tenant_id": 1, "code": "task_resource_color_青色", "label": "青色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:45:40", "updated_at": "2025-04-28 15:45:40"}, {"id": 619, "tenant_id": 1, "code": "task_resource_color_粉色", "label": "粉色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:45:44", "updated_at": "2025-04-28 15:45:44"}, {"id": 620, "tenant_id": 1, "code": "task_resource_color_黑色", "label": "黑色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:46:00", "updated_at": "2025-04-28 15:46:00"}, {"id": 621, "tenant_id": 1, "code": "task_resource_color_白色", "label": "白色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:46:04", "updated_at": "2025-04-28 15:46:04"}, {"id": 622, "tenant_id": 1, "code": "task_resource_color_金色", "label": "金色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:46:13", "updated_at": "2025-04-28 15:46:13"}, {"id": 623, "tenant_id": 1, "code": "task_resource_color_银色", "label": "银色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:46:19", "updated_at": "2025-04-28 15:46:19"}, {"id": 624, "tenant_id": 1, "code": "task_resource_color_铜色", "label": "铜色", "pid": 589, "level": 3, "status": 2, "created_at": "2025-04-28 15:46:26", "updated_at": "2025-04-28 15:46:26"}, {"id": 625, "tenant_id": 1, "code": "task_resource_optical_反光", "label": "反光", "pid": 591, "level": 3, "status": 2, "created_at": "2025-04-28 15:47:10", "updated_at": "2025-04-28 15:47:10"}, {"id": 626, "tenant_id": 1, "code": "task_resource_optical_发光", "label": "发光", "pid": 591, "level": 3, "status": 2, "created_at": "2025-04-28 15:47:40", "updated_at": "2025-04-28 15:47:40"}, {"id": 627, "tenant_id": 1, "code": "task_resource_optical_反射", "label": "反射", "pid": 591, "level": 3, "status": 2, "created_at": "2025-04-28 15:47:44", "updated_at": "2025-04-28 15:47:44"}, {"id": 628, "tenant_id": 1, "code": "task_resource_material_金属", "label": "金属", "pid": 593, "level": 3, "status": 2, "created_at": "2025-04-28 15:47:56", "updated_at": "2025-04-28 15:47:56"}, {"id": 629, "tenant_id": 1, "code": "task_resource_material_硅胶", "label": "硅胶", "pid": 593, "level": 3, "status": 2, "created_at": "2025-04-28 15:48:01", "updated_at": "2025-04-28 15:48:01"}, {"id": 630, "tenant_id": 1, "code": "task_resource_material_塑料", "label": "塑料", "pid": 593, "level": 3, "status": 2, "created_at": "2025-04-28 15:48:10", "updated_at": "2025-04-28 15:48:10"}, {"id": 631, "tenant_id": 1, "code": "task_resource_material_布料", "label": "布料", "pid": 593, "level": 3, "status": 2, "created_at": "2025-04-28 15:48:15", "updated_at": "2025-04-28 15:48:15"}, {"id": 632, "tenant_id": 1, "code": "task_resource_material_木质", "label": "木质", "pid": 593, "level": 3, "status": 2, "created_at": "2025-04-28 15:48:24", "updated_at": "2025-04-28 15:48:24"}, {"id": 633, "tenant_id": 1, "code": "task_resource_material_石质", "label": "石质", "pid": 593, "level": 3, "status": 2, "created_at": "2025-04-28 15:48:33", "updated_at": "2025-04-28 15:48:33"}, {"id": 634, "tenant_id": 1, "code": "task_resource_shape_圆形", "label": "圆形", "pid": 597, "level": 3, "status": 2, "created_at": "2025-04-28 15:49:06", "updated_at": "2025-04-28 15:49:06"}, {"id": 635, "tenant_id": 1, "code": "task_resource_shape_柱形", "label": "柱形", "pid": 597, "level": 3, "status": 2, "created_at": "2025-04-28 15:49:11", "updated_at": "2025-04-28 15:49:11"}, {"id": 636, "tenant_id": 1, "code": "task_resource_shape_球形", "label": "球形", "pid": 597, "level": 3, "status": 2, "created_at": "2025-04-28 15:49:21", "updated_at": "2025-04-28 15:49:21"}, {"id": 637, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Write（书写）", "label": "Write（书写）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-04-29 13:14:21", "updated_at": "2025-04-29 13:14:45"}, {"id": 638, "tenant_id": 1, "code": "task_project_Copilot", "label": "Copilot", "pid": 449, "level": 3, "status": 2, "created_at": "2025-04-30 15:00:43", "updated_at": "2025-04-30 15:00:43"}, {"id": 639, "tenant_id": 1, "code": "task_atomic_ability_single_arm_Clip(夹住）", "label": "Clip(夹住）", "pid": 260, "level": 3, "status": 2, "created_at": "2025-05-06 14:31:52", "updated_at": "2025-05-06 14:31:52"}, {"id": 641, "tenant_id": 1, "code": "task_project_历史数据重刷", "label": "历史数据重刷", "pid": 449, "level": 3, "status": 2, "created_at": "2025-05-12 16:34:51", "updated_at": "2025-05-12 16:34:51"}, {"id": 642, "tenant_id": 1, "code": "task_resource_material_纸质", "label": "纸质", "pid": 593, "level": 3, "status": 2, "created_at": "2025-05-13 11:03:08", "updated_at": "2025-05-13 11:03:08"}, {"id": 643, "tenant_id": 1, "code": "task_resource_kinematic_禁止", "label": "禁止", "pid": 599, "level": 3, "status": 2, "created_at": "2025-05-13 11:03:51", "updated_at": "2025-05-13 11:03:51"}, {"id": 644, "tenant_id": 1, "code": "task_resource_color_其他", "label": "其他", "pid": 589, "level": 3, "status": 2, "created_at": "2025-05-13 11:10:33", "updated_at": "2025-05-13 11:10:33"}, {"id": 645, "tenant_id": 1, "code": "task_resource_morphology_刚性", "label": "刚性", "pid": 595, "level": 3, "status": 2, "created_at": "2025-05-13 12:01:11", "updated_at": "2025-05-13 12:01:11"}, {"id": 646, "tenant_id": 1, "code": "task_resource_morphology_柔性", "label": "柔性", "pid": 595, "level": 3, "status": 2, "created_at": "2025-05-13 12:01:18", "updated_at": "2025-05-13 12:01:18"}, {"id": 647, "tenant_id": 1, "code": "task_resource_morphology_液态", "label": "液态", "pid": 595, "level": 3, "status": 2, "created_at": "2025-05-13 12:01:23", "updated_at": "2025-05-13 12:01:23"}, {"id": 648, "tenant_id": 1, "code": "task_collect_type", "label": "采集方式", "pid": 1, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:03", "updated_at": "2025-07-03 21:20:03"}, {"id": 649, "tenant_id": 2, "code": "task_collect_type", "label": "采集方式", "pid": 490, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:03", "updated_at": "2025-07-03 21:20:03"}, {"id": 651, "tenant_id": 1, "code": "task_collect_type_normal", "label": "常规", "pid": 648, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 652, "tenant_id": 2, "code": "task_collect_type_normal", "label": "常规", "pid": 649, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 654, "tenant_id": 1, "code": "task_collect_type_copilot", "label": "Copilot", "pid": 648, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 655, "tenant_id": 2, "code": "task_collect_type_copilot", "label": "Copilot", "pid": 649, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 657, "tenant_id": 1, "code": "task_visual_range", "label": "任务视距", "pid": 1, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 658, "tenant_id": 2, "code": "task_visual_range", "label": "任务视距", "pid": 490, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 660, "tenant_id": 1, "code": "task_visual_range_inside", "label": "视距内", "pid": 657, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 661, "tenant_id": 2, "code": "task_visual_range_inside", "label": "视距内", "pid": 658, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 663, "tenant_id": 1, "code": "task_visual_range_beyond", "label": "超视距", "pid": 657, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 664, "tenant_id": 2, "code": "task_visual_range_beyond", "label": "超视距", "pid": 658, "level": 2, "status": 4, "created_at": "2025-07-03 21:20:04", "updated_at": "2025-07-03 21:20:04"}, {"id": 674, "tenant_id": 1, "code": "task_camera_rgbd_head_test", "label": "head_test", "pid": 450, "level": 3, "status": 2, "created_at": "2025-07-04 16:57:48", "updated_at": "2025-07-04 16:57:48"}, {"id": 707, "tenant_id": 1, "code": "auto_test_auto_update_level1", "label": "auto_update_level1", "pid": 0, "level": 1, "status": 2, "created_at": "2025-07-22 10:20:34", "updated_at": "2025-07-22 10:20:35"}, {"id": 708, "tenant_id": 1, "code": "auto_test_auto_update_level1_auto_test_level2", "label": "auto_test_level2", "pid": 707, "level": 2, "status": 2, "created_at": "2025-07-22 10:20:35", "updated_at": "2025-07-22 10:20:35"}, {"id": 709, "tenant_id": 1, "code": "auto_test_level1", "label": "auto_test_level1", "pid": 0, "level": 1, "status": 2, "created_at": "2025-07-22 10:24:07", "updated_at": "2025-07-22 10:24:07"}, {"id": 710, "tenant_id": 1, "code": "auto_test_level1_auto_test_level2", "label": "auto_test_level2", "pid": 709, "level": 2, "status": 2, "created_at": "2025-07-22 10:24:08", "updated_at": "2025-07-22 10:24:08"}, {"id": 714, "tenant_id": 1, "code": "task_atomic_ability_single_arm_抓取", "label": "抓取", "pid": 260, "level": 3, "status": 2, "created_at": "2025-07-25 19:49:30", "updated_at": "2025-07-25 19:49:30"}, {"id": 715, "tenant_id": 1, "code": "task_end_kind_ctek_gripper_120s", "label": "ctek_gripper_120s", "pid": 248, "level": 3, "status": 2, "created_at": "2025-07-25 19:50:20", "updated_at": "2025-07-25 19:50:20"}, {"id": 716, "tenant_id": 1, "code": "task_default_review_label_默认标签", "label": "默认标签", "pid": 422, "level": 3, "status": 2, "created_at": "2025-07-27 16:22:39", "updated_at": "2025-07-27 16:22:39"}, {"id": 718, "tenant_id": 1, "code": "task_error_cause", "label": "错因标签", "pid": 1, "level": 1, "status": 4, "created_at": "2025-07-28 17:30:26", "updated_at": "2025-07-28 17:30:26"}, {"id": 719, "tenant_id": 2, "code": "task_error_cause", "label": "错因标签", "pid": 490, "level": 1, "status": 4, "created_at": "2025-07-28 17:30:26", "updated_at": "2025-07-28 17:30:26"}, {"id": 721, "tenant_id": 1, "code": "task_error_cause_指令跟随错误", "label": "指令跟随错误", "pid": 718, "level": 2, "status": 2, "created_at": "2025-07-28 17:43:41", "updated_at": "2025-07-29 09:07:15"}, {"id": 722, "tenant_id": 1, "code": "task_error_cause_其他", "label": "其他", "pid": 718, "level": 2, "status": 2, "created_at": "2025-07-28 17:49:20", "updated_at": "2025-07-28 17:49:20"}, {"id": 728, "tenant_id": 1, "code": "task_resource_kind_auto_test_level1", "label": "auto_test_level1", "pid": 243, "level": 3, "status": 2, "created_at": "2025-07-28 20:00:04", "updated_at": "2025-07-28 20:00:04"}, {"id": 729, "tenant_id": 1, "code": "task_error_cause_操作过程问题", "label": "操作过程问题", "pid": 718, "level": 2, "status": 2, "created_at": "2025-07-29 09:10:56", "updated_at": "2025-07-29 09:10:56"}, {"id": 730, "tenant_id": 1, "code": "task_error_cause_动作结果失败", "label": "动作结果失败", "pid": 718, "level": 2, "status": 2, "created_at": "2025-07-29 09:11:15", "updated_at": "2025-07-29 09:11:15"}, {"id": 732, "tenant_id": 1, "code": "task_error_cause_指令跟随错误_目标物错误", "label": "目标物错误", "pid": 721, "level": 3, "status": 2, "created_at": "2025-07-29 09:13:31", "updated_at": "2025-07-29 09:13:31"}, {"id": 733, "tenant_id": 1, "code": "task_error_cause_指令跟随错误_未按步骤执行", "label": "未按步骤执行", "pid": 721, "level": 3, "status": 2, "created_at": "2025-07-29 09:13:45", "updated_at": "2025-07-29 09:13:45"}, {"id": 734, "tenant_id": 1, "code": "task_error_cause_指令跟随错误_放置位置与指令不符", "label": "放置位置与指令不符", "pid": 721, "level": 3, "status": 2, "created_at": "2025-07-29 09:14:01", "updated_at": "2025-07-29 09:14:01"}, {"id": 740, "tenant_id": 1, "code": "task_error_cause_操作过程问题_空夹", "label": "空夹", "pid": 729, "level": 3, "status": 2, "created_at": "2025-07-29 09:21:22", "updated_at": "2025-07-29 09:21:22"}, {"id": 741, "tenant_id": 1, "code": "task_error_cause_操作过程问题_掉落", "label": "掉落", "pid": 729, "level": 3, "status": 2, "created_at": "2025-07-29 09:21:26", "updated_at": "2025-07-29 09:21:26"}, {"id": 742, "tenant_id": 1, "code": "task_error_cause_操作过程问题_碰撞", "label": "碰撞", "pid": 729, "level": 3, "status": 2, "created_at": "2025-07-29 09:21:32", "updated_at": "2025-07-29 09:21:32"}, {"id": 743, "tenant_id": 1, "code": "task_error_cause_操作过程问题_无意义操作", "label": "无意义操作", "pid": 729, "level": 3, "status": 2, "created_at": "2025-07-29 09:21:38", "updated_at": "2025-07-29 09:21:38"}, {"id": 744, "tenant_id": 1, "code": "task_error_cause_操作过程问题_操作停顿", "label": "操作停顿", "pid": 729, "level": 3, "status": 2, "created_at": "2025-07-29 09:21:43", "updated_at": "2025-07-29 09:21:43"}, {"id": 745, "tenant_id": 1, "code": "task_error_cause_指令跟随错误_其他", "label": "其他", "pid": 721, "level": 3, "status": 2, "created_at": "2025-07-29 10:34:18", "updated_at": "2025-07-29 10:34:18"}, {"id": 746, "tenant_id": 1, "code": "task_error_cause_操作过程问题_其他", "label": "其他", "pid": 729, "level": 3, "status": 2, "created_at": "2025-07-29 10:34:27", "updated_at": "2025-07-29 10:34:27"}, {"id": 747, "tenant_id": 1, "code": "task_error_cause_动作结果失败_叠衣服不整齐", "label": "叠衣服不整齐", "pid": 730, "level": 3, "status": 2, "created_at": "2025-07-29 10:37:34", "updated_at": "2025-07-29 10:37:34"}, {"id": 748, "tenant_id": 1, "code": "task_error_cause_动作结果失败_开门没打开", "label": "开门没打开", "pid": 730, "level": 3, "status": 2, "created_at": "2025-07-29 10:37:42", "updated_at": "2025-07-29 10:37:42"}, {"id": 749, "tenant_id": 1, "code": "task_error_cause_动作结果失败_倒水溢出", "label": "倒水溢出", "pid": 730, "level": 3, "status": 2, "created_at": "2025-07-29 10:37:54", "updated_at": "2025-07-29 10:37:54"}, {"id": 750, "tenant_id": 1, "code": "task_error_cause_动作结果失败_其他", "label": "其他", "pid": 730, "level": 3, "status": 2, "created_at": "2025-07-29 10:38:16", "updated_at": "2025-07-29 10:38:16"}]