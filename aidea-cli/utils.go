package main

import (
	"code.agibot.com/agibot_cloud/aim_stage/iam_sdk/iamsdk"
	"encoding/json"
	"fmt"
	"gopkg.in/yaml.v3"
	"os"
	"strings"
)

const CommonPermission = "api_permission_common_get"

// IamConfig 定义 YAML 配置文件的结构
type IamConfig struct {
	Iam struct {
		Organization string `yaml:"organization"`
		Endpoint     string `yaml:"endpoint"`
		AppName      string `yaml:"appName"`
		AppId        string `yaml:"appId"`
		AppSecret    string `yaml:"appSecret"`
		CallbackUrl  string `yaml:"callbackUrl"`
		OriginUrl    string `yaml:"originUrl"`
		ApiModel     string `yaml:"apiModel"`
		RbacModel    string `yaml:"rbacModel"`
		Cert         string `yaml:"cert"`
	} `yaml:"iam"`
}

// PermissionItem 权限项结构
type PermissionItem struct {
	Id       string           `json:"id"`
	Label    string           `json:"label"`
	Api      string           `json:"api,omitempty"`
	Children []PermissionItem `json:"children,omitempty"`
}

func loadConfig(configPath string) (*IamConfig, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config IamConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// loadPermissions 加载权限配置
func loadPermissions(permPath string) ([]PermissionItem, error) {
	data, err := os.ReadFile(permPath)
	if err != nil {
		return nil, fmt.Errorf("读取权限配置文件失败: %v", err)
	}

	var permissions []PermissionItem
	if err := json.Unmarshal(data, &permissions); err != nil {
		return nil, fmt.Errorf("解析权限配置文件失败: %v", err)
	}

	return permissions, nil
}

// extractApiPermissions 提取API权限
func extractApiPermissions(items []PermissionItem) map[string]string {
	apiPerms := make(map[string]string)
	var extract func(items []PermissionItem)

	extract = func(items []PermissionItem) {
		for _, item := range items {
			if item.Api != "" {
				parts := strings.Split(item.Api, "@")
				if len(parts) == 2 {
					apiPerms[item.Api] = item.Api
				}
			}
			if len(item.Children) > 0 {
				extract(item.Children)
			}
		}
	}
	extract(items)
	return apiPerms
}

// extractRbacPermissions 提取RBAC权限
func extractRbacPermissions(items []PermissionItem) map[string]string {
	rbacPerms := make(map[string]string)
	var extract func(items []PermissionItem)

	extract = func(items []PermissionItem) {
		for _, item := range items {
			if item.Id != "" {
				rbacPerms[item.Id] = item.Label
			}
			if len(item.Children) > 0 {
				extract(item.Children)
			}
		}
	}

	extract(items)
	return rbacPerms
}

// updatePermissions 更新权限
func updatePermissions(client *iamsdk.Client, accessToken string, roleName string, permPath string) error {
	// 加载权限配置
	permissions, err := loadPermissions(permPath)
	if err != nil {
		return err
	}

	// 提取API和RBAC权限
	apiPerms := extractApiPermissions(permissions)
	rbacPerms := extractRbacPermissions(permissions)

	// 获取现有的所有权限
	existingPerms, err := client.GetPermissions()
	if err != nil {
		return fmt.Errorf("获取现有权限列表失败: %v", err)
	}

	// 创建需要保留的权限ID集合
	keepPerms := make(map[string]bool)
	for id := range apiPerms {
		keepPerms[id] = true
	}
	for id := range rbacPerms {
		keepPerms[id] = true
	}

	// 删除不在配置文件中的权限
	for _, perm := range existingPerms {
		if perm.Name == CommonPermission {
			continue
		}
		if !keepPerms[perm.Name] {
			_, err := client.DeletePermission(accessToken, perm.Name)
			if err != nil {
				fmt.Printf("删除权限 %s 失败: %v\n", perm.Name, err)
				continue
			}
			fmt.Printf("已删除权限: %s\n", perm.Name)
		}
	}

	// 更新API权限
	for id, apiInfo := range apiPerms {
		// 检查权限是否存在
		existingPerm, err := client.GetPermission(id)
		if err != nil {
			fmt.Printf("检查API权限 %s 失败: %v\n", id, err)
			continue
		}

		parts := strings.Split(apiInfo, "@")
		if len(parts) != 2 {
			continue
		}
		methodPath := strings.Split(parts[0], ":")
		if len(methodPath) != 2 {
			continue
		}

		method := strings.ToUpper(methodPath[0])
		path := methodPath[1]

		if existingPerm == nil {
			// 如果权限不存在，创建新权限
			_, err = client.AddApiPermission(accessToken, id, []string{roleName}, []string{path}, []string{method})
			if err != nil {
				fmt.Printf("创建API权限 %s 失败: %v\n", id, err)
				continue
			}
			fmt.Printf("已创建API权限: %s\n", id)
		} else {
			//fmt.Printf("API权限: %s 已存在\n", id)
		}
	}

	// 更新RBAC权限
	for id := range rbacPerms {
		// 检查权限是否存在
		existingPerm, err := client.GetPermission(id)
		if err != nil {
			fmt.Printf("检查RBAC权限 %s 失败: %v\n", id, err)
			continue
		}

		if existingPerm == nil {
			// 如果权限不存在，创建新权限
			_, err = client.AddRbacPermission(accessToken, id, []string{roleName}, []string{id})
			if err != nil {
				fmt.Printf("创建RBAC权限 %s 失败: %v\n", id, err)
				continue
			}
			fmt.Printf("已创建RBAC权限: %s\n", id)
		} else {
			//fmt.Printf("RBAC权限: %s 已存在\n", id)
		}
	}
	return nil
}
