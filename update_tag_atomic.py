import json
import pymysql

# 1. 读取 old_tag.json 和 tag.json，建立旧ID到新ID的映射
with open('old_tag.json', 'r', encoding='utf-8') as f:
    old_tags = json.load(f)

with open('tag.json', 'r', encoding='utf-8') as f:
    new_tags = json.load(f)

old_code2id = {item['code']: item['id'] for item in old_tags}
new_code2id = {item['code']: item['id'] for item in new_tags}

oldid2newid = {}
for code, old_id in old_code2id.items():
    if code in new_code2id:
        oldid2newid[old_id] = new_code2id[code]

print("旧ID到新ID映射：", oldid2newid)

# 2. 连接数据库，批量更新 action_step 字段
# 请根据实际情况修改数据库连接参数
DB_CONFIG = {
    'host': 'rm-bp1ltqb6yafzce28eao.mysql.rds.aliyuncs.com',
    'user': 'root',
    'password': 'Wa4P2h4j2kRYCR3BxV',
    'database': 'aim_data',
    'charset': 'utf8mb4'
}

def update_action_step():
    db = pymysql.connect(**DB_CONFIG)
    cursor = db.cursor()
    cursor.execute("SELECT id, action_step FROM job_template WHERE action_step IS NOT NULL AND action_step != ''")
    rows = cursor.fetchall()
    for row in rows:
        tag_id, action_step_str = row
        try:
            steps = json.loads(action_step_str)
        except Exception as e:
            print(f"解析JSON失败，id={tag_id}，错误：{e}")
            continue
        changed = False
        for step in steps:
            atomic = step.get('atomic')
            if atomic in oldid2newid:
                step['atomic'] = oldid2newid[atomic]
                changed = True
        if changed:
            new_action_step_str = json.dumps(steps, ensure_ascii=False)
            cursor.execute(
                "UPDATE job_template SET action_step=%s WHERE id=%s",
                (new_action_step_str, tag_id)
            )
            print(f"已更新id={tag_id}")
    db.commit()
    cursor.close()
    db.close()
    print("全部更新完成！")

if __name__ == '__main__':
    update_action_step() 