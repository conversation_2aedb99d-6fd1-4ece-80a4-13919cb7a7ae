package logger

import (
	"os"

	"github.com/sirupsen/logrus"
)

var CustomLog = logrus.New()

func init() {
	// 设置日志级别为Info
	CustomLog.SetLevel(logrus.InfoLevel)

	// 设置日志输出到标准输出
	CustomLog.Out = os.Stdout

	// 设置钩子（hook）以实现Error级别的日志输出到标准错误输出
	CustomLog.AddHook(&errorHook{})
}

type errorHook struct{}

func (h *errorHook) Levels() []logrus.Level {
	return []logrus.Level{logrus.ErrorLevel}
}

func (h *errorHook) Fire(entry *logrus.Entry) error {
	entry.Message = "ERROR: " + entry.Message
	return nil
}
