apiVersion: batch/v1
kind: Job
metadata:
  labels:
    app: aim-data-dataclone
  name: aim-data-dataclone-{{ .cloneID }}
spec:
  template:
    metadata:
      labels:
        app: aim-data-dataclone
    spec:
      containers:
      - args:
        - --clone-id
        - "{{ .cloneID }}"
        image: registry.agibot.com/agibot-tech/aimdataclone:latest
        imagePullPolicy: Always
        name: aimdataclone
        resources:
          limits:
            cpu: "4"
            memory: 8000Mi
          requests:
            cpu: "1"
            memory: 200Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /app/config
          name: config
      dnsPolicy: ClusterFirst
      restartPolicy: Never
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          name: aim-data-cm
        name: config
