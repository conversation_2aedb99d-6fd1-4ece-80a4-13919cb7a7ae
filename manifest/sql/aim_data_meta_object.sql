CREATE DATABASE IF NOT EXISTS `aim_data`;

USE `aim_data`;

DROP TABLE IF EXISTS `meta_object`;
CREATE TABLE `meta_object`
(
    `id`                int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `project_id`        int(10) unsigned NOT NULL COMMENT 'project id',
    `object_id`         int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'object relative folder object id',
    `object_path`       varchar(255) COMMENT 'object store path',
    `object_name`       varchar(63) NOT NULL COMMENT 'object name',
    `object_type`       varchar(63) NOT NULL COMMENT 'object type',
    `file_extension`    varchar(255) COMMENT 'sub object file extension limit in folder',
    `size`              int(10) unsigned COMMENT 'object size',
    `batch_id`          varchar(127) COMMENT 'file object upload batch id',
    `mime`              varchar(63) COMMENT 'file object mime type',
    `md5sum`            varchar(63) COMMENT 'file object md5sum',
    `status`            varchar(63) COMMENT 'object status',
    `source`            varchar(63) COMMENT 'object source',
    `owner`             varchar(63) NOT NULL COMMENT 'object owner',
    `owner_id`          varchar(127) NOT NULL COMMENT 'object owner id',
    `annotator`         varchar(63) COMMENT 'object annotator',
    `annotator_id`      varchar(127) COMMENT 'object annotator id',
    `prosecutor`        varchar(63) COMMENT 'object prosecutor',
    `prosecutor_id`     varchar(127) COMMENT 'object prosecutor id',
    `annotation`        text COMMENT 'object annotation',
    `created_at`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created Time',
    `updated_at`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated Time',
    `deleted_at`        bigint DEFAULT 0 COMMENT 'Deleted Time',
    PRIMARY KEY (`id`),
    UNIQUE (`project_id`, `object_id`, `object_name`, `deleted_at`),
    INDEX (`project_id`, `object_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
