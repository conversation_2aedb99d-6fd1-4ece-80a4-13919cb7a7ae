create table dataset
(
    id               int unsigned auto_increment
        primary key,
    dataset_name     varchar(127) default ''                not null comment '数据集名称',
    dataset_eng_name varchar(127) default ''                not null comment '数据集名称（英）',
    source           tinyint      default 0                 not null comment '数据来源：1:平台任务 2:文件导入',
    data_size        bigint                                 not null comment '单位：Byte',
    status           tinyint      default 0                 not null comment '数据集状态 1：新建 2:创建中 3：创建完成 ',
    remark           text                                   not null,
    episode_cnt      int          default 0                 not null comment '数据集下对应的episode数据量',
    dataset_path     varchar(127) default ''                not null comment '数据集元数据存放地址',
    dataset_summary  text                                   not null comment '数据集概览',
    creator_name     varchar(127) default '0'               not null,
    create_id        varchar(127) default ''                not null,
    update_id        varchar(127) default ''                not null,
    created_at       datetime     default CURRENT_TIMESTAMP not null,
    updated_at       datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    locked_until     datetime                               null comment '锁定截止时间',
    locked_by        varchar(64)  default ''                not null,
    retry_cnt        int          default 0                 not null comment '重试次数',
    deleted_at       bigint       default 0                 not null
);


create index idx_deleted
    on dataset (deleted_at);

create index idx_filter
    on dataset (source, status, deleted_at);
