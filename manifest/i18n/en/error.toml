"gf.gvalid.rule.required" = "The {field} field is required"
"gf.gvalid.rule.required-if" = "The {field} field is required"
"gf.gvalid.rule.required-unless" = "The {field} field is required"
"gf.gvalid.rule.required-with" = "The {field} field is required"
"gf.gvalid.rule.required-with-all" = "The {field} field is required"
"gf.gvalid.rule.required-without" = "The {field} field is required"
"gf.gvalid.rule.required-without-all" = "The {field} field is required"
"gf.gvalid.rule.date" = "The {field} value `{value}` is not a valid date"
"gf.gvalid.rule.datetime" = "The {field} value `{value}` is not a valid datetime"
"gf.gvalid.rule.date-format" = "The {field} value `{value}` does not match the format: {pattern}"
"gf.gvalid.rule.email" = "The {field} value `{value}` is not a valid email address"
"gf.gvalid.rule.phone" = "The {field} value `{value}` is not a valid phone number"
"gf.gvalid.rule.telephone" = "The {field} value `{value}` is not a valid telephone number"
"gf.gvalid.rule.passport" = "The {field} value `{value}` is not a valid passport format"
"gf.gvalid.rule.password" = "The {field} value `{value}` is not a valid password format"
"gf.gvalid.rule.password2" = "The {field} value `{value}` is not a valid password format"
"gf.gvalid.rule.password3" = "The {field} value `{value}` is not a valid password format"
"gf.gvalid.rule.postcode" = "The {field} value `{value}` is not a valid postcode format"
"gf.gvalid.rule.resident-id" = "The {field} value `{value}` is not a valid resident id number"
"gf.gvalid.rule.bank-card" = "The {field} value `{value}` is not a valid bank card number"
"gf.gvalid.rule.qq" = "The {field} value `{value}` is not a valid QQ number"
"gf.gvalid.rule.ip" = "The {field} value `{value}` is not a valid IP address"
"gf.gvalid.rule.ipv4" = "The {field} value `{value}` is not a valid IPv4 address"
"gf.gvalid.rule.ipv6" = "The {field} value `{value}` is not a valid IPv6 address"
"gf.gvalid.rule.mac" = "The {field} value `{value}` is not a valid MAC address"
"gf.gvalid.rule.url" = "The {field} value `{value}` is not a valid URL address"
"gf.gvalid.rule.domain" = "The {field} value `{value}` is not a valid domain format"
"gf.gvalid.rule.length" = "The {field} value `{value}` length must be between {min} and {max}"
"gf.gvalid.rule.min-length" = "The {field} value `{value}` length must be equal or greater than {min}"
"gf.gvalid.rule.max-length" = "The {field} value `{value}` length must be equal or lesser than {max}"
"gf.gvalid.rule.size" = "The {field} value `{value}` length must be {size}"
"gf.gvalid.rule.between" = "The {field} value `{value}` must be between {min} and {max}"
"gf.gvalid.rule.min" = "The {field} value `{value}` must be equal or greater than {min}"
"gf.gvalid.rule.max" = "The {field} value `{value}` must be equal or lesser than {max}"
"gf.gvalid.rule.json" = "The {field} value `{value}` is not a valid JSON string"
"gf.gvalid.rule.xml" = "The {field} value `{value}` is not a valid XML string"
"gf.gvalid.rule.array" = "The {field} value `{value}` is not an array"
"gf.gvalid.rule.integer" = "The {field} value `{value}` is not an integer"
"gf.gvalid.rule.boolean" = "The {field} value `{value}` field must be true or false"
"gf.gvalid.rule.same" = "The {field} value `{value}` must be the same as field {pattern}"
"gf.gvalid.rule.different" = "The {field} value `{value}` must be different from field {pattern}"
"gf.gvalid.rule.in" = "The {field} value `{value}` is not in acceptable range: {pattern}"
"gf.gvalid.rule.not-in" = "The {field} value `{value}` must not be in range: {pattern}"
"gf.gvalid.rule.regex" = "The {field} value `{value}` must be in regex of: {pattern}"
"gf.gvalid.rule.gf.gvalid.rule.__default__" = "The :attribute value `:value` is invalid"


"用户登录失败" = "User login failed"
"创建用户失败" = "User creation failed"
"删除用户失败" = "User deletion failed"
"更新用户失败" = "User update failed"
"获取用户列表失败" = "Failed to obtain user list"
"绑定角色失败" = "Failed to bind role"
"创建角色失败" = "Failed to create role"
"更新角色失败" = "Failed to update role"
"删除角色失败" = "Failed to delete role"
"获取角色列表失败" = "Failed to obtain role list"
"获取权限列表失败" = "Failed to obtain permission list"
"删除项目失败" = "Failed to delete project"
"创建项目失败" = "Failed to create project"
"更新项目失败" = "Failed to update project"
"创建对象失败" = "Failed to create object"
"删除对象失败" = "Failed to delete object"
"更新对象失败" = "Failed to update object"
"重名对象已存在" = "An object with the same name already exists"
"标注失败" = "Labeling failed"
"校验失败" = "Verification failed"
"定时任务更新项目信息失败" = "Failed to update project information."
"获取数据落盘信息失败" = "Failed to obtain data disk information"
"创建数采记录失败" = "Failed to create data record"
"查询采集记录失败" = "Failed to query collection record"
"更改采集记录失败" = "Failed to change record"
"修改采集记录失败" = "Failed to modify record"
"全局搜索episode信息失败" = "Failed to search global episode information"
"创建任务失败" = "Failed to create task"
"获取任务列表失败" = "Failed to obtain task list"
"添加任务用户失败" = "Failed to add task user"
"更新任务用户失败" = "Failed to update task user"
"获取任务用户列表失败" = "Failed to obtain task user list"
"更新任务失败" = "Failed to update task"
"删除任务失败" = "Failed to delete task"
"获取任务详情失败" = "Failed to obtain task details"
"创建委派记录失败" = "Failed to create delegation record"
"获取委派记录失败" = "Failed to obtain delegation record"
"获取单一动作采集员统计失败" = "Failed to obtain collector info"
"创建场景失败" = "Failed to create scene"
"删除场景失败" = "Failed to delete scene"
"更新场景失败" = "Failed to update scene"
"获取场景详情失败" = "Failed to obtain scene details"
"查询场景列表失败" = "Failed to query scene list"
"创建物体失败" = "Failed to create object"
"删除物体失败" = "Failed to delete object"
"更新物体失败" = "Failed to update object "
"获取物体详情失败" = "Failed to obtain object details"
"查询物体列表失败" = "Failed to query object list"
"获取模板数据失败" = "Failed to obtain template info"
"保存模板数据失败" = "Failed to save template"
"获取飞书文档助手凭证失败" = "Failed to obtain Feishu Document Assistant credentials"
"查询动作列表失败" = "Failed to obtain job list"
"导入动作列表失败" = "Failed to import job list"
"新建动作失败" = "Failed to create job"
"删除动作失败" = "Failed to delete job"
"更新动作失败" = "Failed to update job"
"克隆动作失败" = "Failed to clone job"
"获取动作详情失败" = "Failed to obtain job details"
"创建实例任务失败" = "Failed to create instance task"
"创建设备记录失败" = "Failed to create device"
"删除设备记录失败" = "Failed to delete device"
"更新设备记录失败" = "Failed to update device"
"查询设备记录失败" = "Failed to query device"
"查询审核数据失败" = "Failed to query checked episode"
"提交审核记录失败" = "Failed to submit checked episode"
"查询审核标注失败" = "Failed to query episode annotation info"
"获取下一条数据失败" = "Failed to obtain next episode"
"获取一键交付详情失败" = "Failed to obtain fast delivery details"
"一键交付失败" = "Failed to fast deliver"
"查询数据概览信息失败" = "Failed to query overview information"
"获取时间统计失败" = "Failed to obtain time statistics"
"获取用户统计失败" = "Failed to obtain user statistics"
"获取任务采集进度统计失败" = "Failed to obtain task collection progress statistics"
"获取任务审核进度统计失败" = "Failed to obtain task check progress statistics"
"下载审核通过数据失败" = "Failed to download passed data"
"下载有效时长数据失败" = "Failed to download effective data"
"查询用户配置失败" = "Failed to query user configuration"
"删除用户配置失败" = "Failed to delete user configuration"
"更新用户配置失败" = "Failed to update user configuration"
"增加标签失败" = "Failed to add tags"
"查询标签失败" = "Failed to query tags"
"删除标签失败" = "Failed to delete tags"
"更新标签失败" = "Failed to update tags"
"增加发版记录失败" = "Failed to add release notes"
"查询最近的一条发版记录失败" = "Failed to query the latest release notes"
"增加用户组失败" = "Failed to add user groups"
"查询用户组失败" = "Failed to query user groups"
"删除用户组失败" = "Failed to delete user groups"
"更新用户组失败" = "Failed to update user groups"
"用户组名已存在" = "User group name already exists"
"%s已存在" = "%s already exists"
"`%s`已存在" = "`%s` already exists"
"存在任务绑定" = "Task binding exists"
"采集记录查询出错" = "query collection info error"
"审核数据未生成" = "checked data not generated"
"job查询出错" = "Job query error"
"task查询出错" = "Task query error"
"审核数据不存在" = "checked data does not exist"
"Meta.json不存在" = "meta.json does not exist"
"该采集员数据已审核完成，请返回列表继续审核" = "The data has been checked, please return to the list"
"获取采集记录失败" = "Failed to obtain collection data info"
"不在任务白名单，无法审核" = "Not in the task whitelist, can not check."
"任务不是处于运营中状态，无法审核" = "The task is not in operation, cannot be checked."
"采集记录[%d]未找到标注结果" = "No annotation results found in episdoe [%d] "
"采集记录[%d]标注结果解析失败" = "episdoe  [%d] annotation result parsing failed"
"任务已认领，不可重复认领" = "Task has been claimed"
"用户不在白名单里" = "User is not in the whitelist"
"任务不是处于运营中状态，无法领取" = "Task is not in operation, cannot be claimed"
"认领人数已超限" = "The number of collectors has exceeded the limit"
"有未采集完的动作" = "There is an instant task that has not been collected"
"查询到委派记录" = "find some delegation records"
"已有绑定关系的物体，不支持更新上传图片" = "Objects with existing binding relationships do not support update  image"
"存在绑定关系，不支持删除" = "There is a binding relationship, deletion is not supported"
"未查询到动作信息" = "Instant task information was not found"
"未查询到任务信息" = "Task information was not found"
"未查询到委派信息" = "Delegation information was not found"
"设备不存在，请先录入设备信息" = "The device does not exist, please enter the device information first"
"任务不是处于运营中状态，无法采集" = "The task is not in operation and cannot be collected"
"非常规数据达到上限，无法提交" = "Abnormal data has reached the upper limit and cannot be submitted"
"查询为空" = "The query is empty"
"无法删除被领取的实例任务" = "Unable to delete the received instance task"
"未查询到job信息" = "Instance task information was not found"
"没有审核完的数据" = "Data that has not been checked"
"不存在审核完成的数据" = "There is no data that has been checked"
"该数据无法转无效" = "The data cannot be invalidated"
"未查询到episode记录" = "The episode record was not found"
"未登录" = "Not logged in"
"当前用户%s无法删除%s的数据" = "The current user %s cannot delete %s's data"
"无法删除状态为%s的数据" = "Cannot delete data in the state of %s"
"无法标注混合状态的数据[%s，%s]" = "Cannot annotate mixed state data [%s, %s]"
"无法标注%s状态的数据" = "Cannot annotate data in the state of %s"
"%s无法标注%s的数据" = "%s cannot mark %s's data"
"%s无法更改%s的数据" = "%s cannot change %s's data"
"无法删除活跃状态的项目" = "Cannot delete active items"
"当前用户%s无法删除%s的项目" = "The current user %s cannot delete %s's items"
"此标签已存在" = "This label already exists"
"label 不能为空" = "label cannot be empty"
"此标签下无法新增标签" = "Cannot add new tags"
"标签名已存在" = "Tag name already exists"
"此标签绑定了资源，无法删除" = "This tag is bound to resources and cannot be deleted"
"此标签存在子标签，无法删除" = "This tag has subtags and cannot be deleted"
"登录账号%s已存在，请使用其他账号名称" = "Login account %s already exists, please use another account name"
"%s已存在，请使用其他角色名称" = "%s already exists, please use another role name"
"系统角色[%s]禁止删除" = "System role [%s] cannot be deleted"
"sn_code 已存在" = "sn_code already exists"

"API 认证失败" = "Api authentication failed"
"资源不存在" = "Resource does not exist"
"验证失败" = "Validation failed"
"不可用" = "Not available"
"无效的参数" = "Invalid parameter"
"出现内部错误" = "An error occurred internally"


"对象存储上传失败" = "OSS upload failed"
"对象存储下载失败" = "OSS download failed"
"对象存储上传预校验失败" = "OSS pre-signed upload failed"
"对象存储下载预校验失败" = "OSS pre-signed download failed"
"对象存储获取内容预校验失败" = "OSS pre-signed get content failed"
"对象存储删除失败" = "OSS delete object failed"

"数据集保存失败" = "Dataset save failed"
"数据集名称已存在" = "Dataset name already exist"
"数据集名称为空" = "Dataset empty name"
"数据集绑定数据源失败" = "Dataset bind user store failed"
"数据集更新失败" = "Dataset update failed"
"数据集删除失败" = "Dataset delete failed"

"数据源删除失败" = "Data source delete failed"
"数据源删除对象失败" = "Data source delete object failed"
"数据源创建失败" = "Data source create failed"
"数据源名称已存在" = "Data source name already exist"


"不支持删除非无效数据" = "Do not support deleting valid data"
"任务名已存在" = "Task name already exist."
"历史任务无法编辑动作模板" = "Cannot edit action templet of history tasks"
"当前用户[%s]无法更新[%s]的项目" = "Current user [%s] cannot update [%s]"
"无法删除被领取的实例任务[%s]" = "Cannot delete claimed job [%s]"

"更新委派记录失败" = "Failed to update delegation record"
"当前用户不是采集员" = "Current user is not a collector"
"请完成当前采集任务再领取" = "Please finish the current collection task before reclaiming a task"
"用户不在白名单里	" = "Use is not on the white list"
"任务不是处于运营中状态,无法领取" = "Claim failed, task is not in operation"
"查询required_member失败" = "Failed to query required_member"
"认领包数已超限" = "Cannot claim more packages"

"字段值 [%v] 重复" = "Field value [%v] is duplicate"

"增加数据集失败"="Failed add dataset"
"查询数据集失败"="Failed query dataset"
"删除数据集失败"="Failed delete dataset"
"更新数据集失败"="Failed update dataset"
"下载数据集失败"="Failed download dataset"
"刷新数据集失败"="Failed refresh dataset"
"获取数据集详情失败"="Failed get dataset detail"
"数据集英文名称已存在"="The English name of the dataset already exists"
"无效的任务实例ID"="Invalid task instance ID"
"数据集处于创建中，禁止编辑"="Dataset editing is locked during creation"
"数据集处于创建中，禁止删除"="Dataset deletion is locked during creation"
"数据集处于创建中，禁止刷新"="Dataset refresh is locked during creation"
"只有创建成功的数据集可以下载"="Only successfully created datasets can be downloaded"

"上传数据集处理失败" = "Failed to process dataset upload"
"任务关联数量不一致" = "Task association count mismatch"
"预期实例任务数:%d 与实际创建数量:%d 不符" = "Expected number of instance tasks: %d does not match the actual number created: %d"
"任务已认领,不可重复认领" = "Task has been claimed, cannot be claimed again"
"变量不一致，请重新创建新的实例任务" = "Variables inconsistent, please create new instance task"
"数据格式不正确" = "Invalid data format"
"新增租户默认标签失败" = "Failed to add tenant default tag"
"服务器繁忙" = "Server is busy"
"未查询到code为%s的标签" = "Tag with code %s not found"
"未查询到委派记录" = "No delegation record found"
"此标签不可被%s" = "This tag cannot be %s"
"用户不存在" = "User does not exist"
"系统角色[%s]禁止删除." = "System role [%s] cannot be deleted"
"绑定标签失败,未查询到相关标签" = "Failed to bind tag, related tag not found"
"设备类型存在任务绑定" = "Device type has task binding"
"该类型数据集禁止刷新" = "This type of dataset cannot be refreshed"
"通过code查询标签失败" = "Failed to query tag by code"
"业务逻辑错误" = "Business logic error"
"创建设备类型失败" = "Failed to create device type"
"删除设备类型失败" = "Failed to delete device type"
"同步设备记录失败" = "Failed to sync device record"
"复制任务失败" = "Failed to copy task"
"数据库错误" = "Database error"
"更新设备类型失败" = "Failed to update device type"
"上传数据集不能为空" = "Upload dataset cannot be empty"
"查询设备类型失败" = "Failed to query device type"
"缓存设备记录失败" = "Failed to cache device records"
"获取帧可视化信息失败" = "Failed to retrieve frame visualization information"




# === 新增的翻译 ===

"数据集已在同步中，没有新数据产生" = "Dataset is already syncing; no new data generated"
"数据集正在同步中，禁止修改同步策略" = "Dataset sync in progress; sync policy modification not allowed"
"数据集状态不正确，禁止修改同步策略" = "Dataset status is invalid; sync policy modification prohibited"
"获取数据集元数据失败" = "Failed to retrieve dataset metadata"
"该类型数据集禁止修改同步策略" = "Modification of sync policy is prohibited for this dataset type"


# === 新增的翻译 ===

"飞书用户禁止删除" = "Feishu users cannot be deleted"


# === 新增的翻译 ===

"数据集同步完成，没有新数据产生" = "Dataset synchronization completed, no new data generated"
"没有新的采集数据产生" = "No new collected data generated"


# === 新增的翻译 ===

"不支持的同步策略类型" = "Unsupported synchronization policy type"
"增加数据集版本失败" = "Failed to add dataset version"
"数据集处于创建中，禁止修改同步状态" = "Dataset creation in progress; cannot modify synchronization status"
"数据集未创建成功" = "Dataset creation failed"
"数据集格式不正确" = "Incorrect dataset format"
"文件格式不正确" = "Incorrect file format"
"更新数据集版本状态失败" = "Failed to update dataset version status"


# === 新增的翻译 ===

"job_template查询出错" = "Error querying job_template"
"文件中的部分 taskID 不存在，请检查后重试" = "Some taskIDs in the file do not exist. Please verify and retry."


# === 新增的翻译 ===

"Task没有关联任何Job" = "Task has no associated Job"


# === 新增的翻译 ===

"原始数据完整性异常：缺失采集时刻动作步骤" = "Raw data integrity exception: Missing acquisition timestamp action steps"
"数据处理异常" = "Data processing exception"
"数据验证失败" = "Data validation failed"


# === 新增的翻译 ===

"参数校验失败" = "Parameter validation failed"


# === 新增的翻译 ===

"episode不存在: episodeId=%d" = "Episode does not exist: episodeId=%d"
"job不存在: jobId=%d" = "Job does not exist: jobId=%d"
"存在重复数据: type=%s, taskId=%d, jobId=%d, episodeId=%d" = "Duplicate data exists: type=%s, taskId=%d, jobId=%d, episodeId=%d"
"未查询到任务模板信息" = "No task template information found"
"未查询到采集记录" = "No collection records found"

# === 新增的翻译 ===
"已经是最后一条数据了"="This is the last episode"