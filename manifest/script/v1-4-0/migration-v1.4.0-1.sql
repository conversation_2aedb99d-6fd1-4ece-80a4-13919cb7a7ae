# 任务标签-采集方式
INSERT INTO tag (tenant_id, code, label, pid, level, status)  SELECT tenant_id, 'task_collect_type', '采集方式', id, 2, 4 FROM tag WHERE code = 'task';
INSERT INTO tag (tenant_id, code, label, pid, level, status)  SELECT tenant_id, 'task_collect_type_normal', '常规', id, 2, 4 FROM tag WHERE code = 'task_collect_type';
INSERT INTO tag (tenant_id, code, label, pid, level, status)  SELECT tenant_id, 'task_collect_type_copilot', 'Copilot', id, 2, 4 FROM tag WHERE code = 'task_collect_type';

# 任务标签-采集方式
INSERT INTO tag (tenant_id, code, label, pid, level, status)  SELECT tenant_id, 'task_visual_range', '任务视距', id, 2, 4 FROM tag WHERE code = 'task';
INSERT INTO tag (tenant_id, code, label, pid, level, status)  SELECT tenant_id, 'task_visual_range_inside', '视距内', id, 2, 4 FROM tag WHERE code = 'task_visual_range';
INSERT INTO tag (tenant_id, code, label, pid, level, status)  SELECT tenant_id, 'task_visual_range_beyond', '超视距', id, 2, 4 FROM tag WHERE code = 'task_visual_range';


# episode status
update episode set episode.status=12,updated_at=updated_at where status=99 and extra is null;
update review set status=12,updated_at=updated_at where status=99;

# 任务模版-动作步骤类型
# ALTER TABLE job_template ADD
#     COLUMN `action_step_type`  tinyint(8) NOT NULL DEFAULT 1 COMMENT '动作步骤类型, 1:预定义固定步骤,2:随机采集' AFTER action_step;
