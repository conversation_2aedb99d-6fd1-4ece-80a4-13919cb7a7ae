# 更新历史任务的设备类型ID
# ！！！注意！！！需要提前创建好对应的设备类型，替换下面的 device_type_id & created_at ！！！
# oymotion_finger_Roh           {ofR_id}
# ctek_gripper_120s             {cg1_id}
# zhiyuan_gripper_omnipicker    {zgm_id}

# 查询 oymotion_finger_Roh
SELECT
    t.id AS task_id,
    t.name AS task_name,
    t.device_type_id AS device_type_id,
    t.updated_at AS updated_at,
    GROUP_CONCAT(DISTINCT tag.label ORDER BY tag.label SEPARATOR ', ') AS associated_tags
FROM task t
         JOIN (
    SELECT tr.resource_id
    FROM tag_rel tr
             JOIN tag ON tr.tag_id = tag.id
    WHERE tr.resource_type = 'task'
      AND tag.label IN ('A2D', 'oymotion_finger_Roh')
    GROUP BY tr.resource_id
    HAVING COUNT(DISTINCT tag.id) = 2
) AS filtered_tasks ON t.id = filtered_tasks.resource_id
         LEFT JOIN tag_rel tr_all ON t.id = tr_all.resource_id AND tr_all.resource_type = 'task'
         LEFT JOIN tag ON tr_all.tag_id = tag.id
WHERE t.deleted_at = 0
GROUP BY t.id, t.name;

-- 更新语句 oymotion_finger_Roh {ofR_id}
UPDATE task
    JOIN (
        SELECT tr.resource_id
        FROM tag_rel tr
                 JOIN tag ON tr.tag_id = tag.id
        WHERE tr.resource_type = 'task'
          AND tag.label IN ('A2D', 'oymotion_finger_Roh')
        GROUP BY tr.resource_id
        HAVING COUNT(DISTINCT tag.id) = 2
    ) AS filtered_tasks ON task.id = filtered_tasks.resource_id
SET task.device_type_id = {ofR_id},
    task.updated_at = task.updated_at  -- 显式保持原值
WHERE task.deleted_at = 0 AND task.created_at < '2025-03-14 12:00:00'
  AND task.device_type_id = 0;


# 查询 ctek_gripper_120s
SELECT
    t.id AS task_id,
    t.name AS task_name,
    t.device_type_id AS device_type_id,
    t.updated_at AS updated_at,
    GROUP_CONCAT(DISTINCT tag.label ORDER BY tag.label SEPARATOR ', ') AS associated_tags
FROM task t
         JOIN (
    SELECT tr.resource_id
    FROM tag_rel tr
             JOIN tag ON tr.tag_id = tag.id
    WHERE tr.resource_type = 'task'
      AND tag.label IN ('A2D', 'ctek_gripper_120s')
    GROUP BY tr.resource_id
    HAVING COUNT(DISTINCT tag.id) = 2
) AS filtered_tasks ON t.id = filtered_tasks.resource_id
         LEFT JOIN tag_rel tr_all ON t.id = tr_all.resource_id AND tr_all.resource_type = 'task'
         LEFT JOIN tag ON tr_all.tag_id = tag.id
WHERE t.deleted_at = 0
GROUP BY t.id, t.name;

-- 更新语句 ctek_gripper_120s {cg1_id}
UPDATE task
    JOIN (
        SELECT tr.resource_id
        FROM tag_rel tr
                 JOIN tag ON tr.tag_id = tag.id
        WHERE tr.resource_type = 'task'
          AND tag.label IN ('A2D', 'ctek_gripper_120s')
        GROUP BY tr.resource_id
        HAVING COUNT(DISTINCT tag.id) = 2
    ) AS filtered_tasks ON task.id = filtered_tasks.resource_id
SET task.device_type_id = {cg1_id},
    task.updated_at = task.updated_at  -- 显式保持原值
WHERE task.deleted_at = 0 AND task.created_at < '2025-03-14 12:00:00'
  AND task.device_type_id = 0;

# 查询 zhiyuan_gripper_omnipicker
SELECT
    t.id AS task_id,
    t.name AS task_name,
    t.device_type_id AS device_type_id,
    t.updated_at AS updated_at,
    GROUP_CONCAT(DISTINCT tag.label ORDER BY tag.label SEPARATOR ', ') AS associated_tags
FROM task t
         JOIN (
    SELECT tr.resource_id
    FROM tag_rel tr
             JOIN tag ON tr.tag_id = tag.id
    WHERE tr.resource_type = 'task'
      AND tag.label IN ('A2D', 'zhiyuan_gripper_omnipicker')
    GROUP BY tr.resource_id
    HAVING COUNT(DISTINCT tag.id) = 2
) AS filtered_tasks ON t.id = filtered_tasks.resource_id
         LEFT JOIN tag_rel tr_all ON t.id = tr_all.resource_id AND tr_all.resource_type = 'task'
         LEFT JOIN tag ON tr_all.tag_id = tag.id
WHERE t.deleted_at = 0
GROUP BY t.id, t.name;

-- 更新语句 zhiyuan_gripper_omnipicker {zgm_id}
UPDATE task
    JOIN (
        SELECT tr.resource_id
        FROM tag_rel tr
                 JOIN tag ON tr.tag_id = tag.id
        WHERE tr.resource_type = 'task'
          AND tag.label IN ('A2D', 'zhiyuan_gripper_omnipicker')
        GROUP BY tr.resource_id
        HAVING COUNT(DISTINCT tag.id) = 2
    ) AS filtered_tasks ON task.id = filtered_tasks.resource_id
SET task.device_type_id = {zgm_id},
    task.updated_at = task.updated_at  -- 显式保持原值
WHERE task.deleted_at = 0 AND task.created_at < '2025-03-14 12:00:00'
  AND task.device_type_id = 0;