// PUT /aim-data-v1
{
  "mappings": {
    "properties": {
      "project": {
        "type": "keyword"
      },
      "data_type": {
        "type": "keyword"
      },
      "data_name": {
        "type": "wildcard"
      },
      "description": {
        "type": "wildcard"
      },
      "user_store_id": {
        "type": "keyword"
      },
      "endpoint": {
        "type": "keyword"
      },
      "path": {
        "type": "keyword"
      },
      "location": {
        "type": "keyword"
      },
      "size": {
        "type": "long"
      },
      "tags": {
        "type": "keyword"
      },
      "data_source": {
        "type": "keyword"
      },
      "language_type": {
        "type": "keyword"
      },
      "data_size": {
        "type": "keyword"
      },
      "text": {
        "type": "keyword"
      },
      "image": {
        "type": "keyword"
      },
      "audio": {
        "type": "keyword"
      },
      "video": {
        "type": "keyword"
      },
      "multimodal": {
        "type": "keyword"
      },
      "robot": {
        "type": "keyword"
      },
      "username": {
        "type": "keyword"
      },
      "user_id": {
        "type": "keyword"
      },
      "created_at": {
        "type": "date",
        "format": "epoch_millis"
      },
      "updated_at": {
        "type": "date",
        "format": "epoch_millis"
      },
      "deleted_at": {
        "type": "date",
        "format": "epoch_millis"
      }
    }
  }
}