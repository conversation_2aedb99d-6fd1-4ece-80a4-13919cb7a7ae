apiVersion: apps/v1
kind: Deployment
metadata:
  name: aim-data-cron-worker
  labels:
    app: aim-data-cron
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aim-data-cron
  template:
    metadata:
      labels:
        app: aim-data-cron
    spec:
      containers:
        - env:
            - name: AIM_DATA_API_URL
              value: http://aim-data-api/api/v1/cron
            - name: DATA_CONSUMER_URL
              value: http://consumer.test.agibot.com
            - name: AIM_DATA_AFTER_PROCESS_API_URL
              value: https://aimdata.test.agibot.com/api/v1/collect/episode
            - name: IS_RUN_DATA_AFTER_PROCESS
              value: 'false'
          image: registry.agibot.com/agibot-tech/aim-data-worker:v1
          imagePullPolicy: Always
          name: server