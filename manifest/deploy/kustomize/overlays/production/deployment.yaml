apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: aim-data-cron
  name: aim-data-cron-worker
  namespace: aim-data-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aim-data-cron
  template:
    metadata:
      labels:
        app: aim-data-cron
      namespace: aim-data-prod
    spec:
      containers:
        - env:
            - name: AIM_DATA_API_URL
              value: http://aim-data-api/api/v1/cron
            - name: DATA_CONSUMER_URL
              value: https://consumer.infra.agibot.com
            - name: AIM_DATA_AFTER_PROCESS_API_URL
              value: http://aim-data-api/api/v1/collect/episode
            - name: IS_RUN_DATA_AFTER_PROCESS
              value: 'true'
          image: registry.agibot.com/agibot-tech/aim-data-worker:test
          imagePullPolicy: Always
          name: server
