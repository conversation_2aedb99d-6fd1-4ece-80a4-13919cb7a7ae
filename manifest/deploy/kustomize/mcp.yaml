apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: aidea-admin-mcp
  name: aidea-admin-mcp
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: aidea-admin-mcp
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: aidea-admin-mcp
    spec:
      containers:
      - env:
        - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
          value: http://jaeger:4317
        command:
        - /app
        - mcp
        - -s
        - -p
        - "80"
        image: registry.agibot.com/agibot-tech/aidea-admin:3ecb4309
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 1
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 2
        name: aidea-admin
        ports:
        - containerPort: 80
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 1
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 2
        resources:
          limits:
            cpu: "4"
            memory: 8000Mi
          requests:
            cpu: 50m
            memory: 100Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /config
          name: config-volume
          readOnly: true
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          items:
          - key: config.yaml
            path: config.yaml
          name: aidea-admin-config
        name: config-volume
---
apiVersion: v1
kind: Service
metadata:
  name: http-aidea-admin-mcp
spec:
  internalTrafficPolicy: Cluster
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - port: 80
    protocol: TCP
    targetPort: 80
  selector:
    app: aidea-admin-mcp
  sessionAffinity: None
  type: ClusterIP


