package aim_data_cron_job

import (
	"context"
	"net/http"
	"os"
	"time"

	"aim_data_worker/logger"
)

// Deprecated
func ClearDirtyData() {
	// 读取环境变量中的接口地址
	apiUrl := os.Getenv("AIM_DATA_API_URL")
	if apiUrl == "" {
		apiUrl = "http://localhost:9000/api/v1/cron"
	}

	// 发送 PUT 请求
	client := &http.Client{}
	req, err := http.NewRequest("PUT", apiUrl, nil)
	if err != nil {
		logger.CustomLog.Error("创建请求失败:", err)
		return
	}

	// 创建一个 context，设置超时时间为5分钟
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	req = req.WithContext(ctx)
	resp, err := client.Do(req)
	if err != nil {
		logger.CustomLog.Error("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()
	logger.CustomLog.Info("PUT 请求成功，状态码:", resp.StatusCode)
}
