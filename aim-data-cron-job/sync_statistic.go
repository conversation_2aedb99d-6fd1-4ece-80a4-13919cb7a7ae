package aim_data_cron_job

import (
	"io"
	"net/http"

	"aim_data_worker/consts"
	"aim_data_worker/logger"
	"aim_data_worker/utility"
)

func SyncStatistic() {
	logger.CustomLog.Info("SyncStatistic Start")
	baseUrl := aimDataApiServer
	ApiPath := consts.AIM_DATA_SYNC_STATISTIC_API
	url, err := utility.BuildURL(baseUrl, ApiPath)
	if err != nil {
		logger.CustomLog.Errorf("创建URL[%s,%s]失败: %v", baseUrl, ApiPath, err)
		return
	}
	client := &http.Client{}
	// 修改为 POST 方法
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		logger.CustomLog.Errorf("创建请求失败: %v", err)
		return
	}
	req.Header.Set(consts.HEADER_CLIENT_TOKEN, aimDataClientToken)
	res, err := client.Do(req)
	if err != nil {
		logger.CustomLog.Errorf("执行%s失败: %v", url, err)
		return
	}
	defer res.Body.Close()

	// 检查状态码
	if res.StatusCode == http.StatusOK {
		logger.CustomLog.Info("缓存更新成功")
	} else {
		body, _ := io.ReadAll(res.Body)
		logger.CustomLog.Errorf("缓存更新失败，状态码: %d, 响应: %s", res.StatusCode, string(body))
	}
}
