package aim_data_cron_job

import (
	"io"
	"net/http"
	
	"aim_data_worker/consts"
	"aim_data_worker/logger"
	"aim_data_worker/utility"
)

func SyncDeviceRecord() {
	if disableSyncDeviceRecord {
		return
	}
	logger.CustomLog.Info("SyncDeviceRecord Start")
	baseUrl := aimDataApiServer
	ApiPath := consts.AIM_DATA_SYNC_DEVICE_API
	url, err := utility.BuildURL(baseUrl, ApiPath)
	if err != nil {
		logger.CustomLog.Errorf("创建URL[%s,%s]失败: %v", baseUrl, ApiPath, err)
		return
	}
	client := &http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.CustomLog.Errorf("创建请求失败: %v", err)
		return
	}
	req.Header.Set(consts.HEADER_CLIENT_TOKEN, aimDataClientToken)
	res, err := client.Do(req)
	if err != nil {
		logger.CustomLog.Errorf("执行%s失败: %v", url, err)
		return
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.CustomLog.Errorf("解析%s返回值失败: %v", url, err)
		return
	}
	logger.CustomLog.Infof("SyncDeviceRecord Res = %s", string(body))
}
