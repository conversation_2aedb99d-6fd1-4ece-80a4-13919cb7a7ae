package aim_data_cron_job

import (
	"os"

	"aim_data_worker/consts"
	"aim_data_worker/logger"
)

var (
	aimDataApiServer        string
	aimDataClientToken      string
	disableSyncDeviceRecord bool // 默认开启
)

func init() {
	// 读取环境变量, 初始化
	aimDataApiServer = os.Getenv(consts.ENV_KEY_AIM_DATA_API_SERVER)
	if aimDataApiServer == "" {
		aimDataApiServer = consts.AIM_DATA_API_SERVER
	}
	// 打印当前使用的API服务器地址
	logger.CustomLog.Infof("当前使用的API服务器地址: %s", aimDataApiServer)

	aimDataClientToken = os.Getenv(consts.ENV_KEY_AIM_DATA_CLIENT_TOKEN)
	if aimDataClientToken == "" {
		aimDataClientToken = consts.AIM_DATA_CLIENT_TOKEN
	}

	aimDataSyncDeviceDisable := os.Getenv(consts.ENV_KEY_AIM_DATA_SYNC_DEVICE_DISABLE)
	if aimDataSyncDeviceDisable == "true" || aimDataSyncDeviceDisable == "1" {
		disableSyncDeviceRecord = true
	}
}
